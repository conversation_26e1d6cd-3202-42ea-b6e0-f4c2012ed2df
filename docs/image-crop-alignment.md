# 图片对齐方式功能

本文档介绍了如何使用修改后的 ImageDrawer 组件来设置图片的对齐方式，实现类似参考代码中的 crop 功能。

## 功能特性

- ✅ 支持 9 种对齐方式
- ✅ 保持图片比例不变
- ✅ 变换时自动重新应用裁剪
- ✅ 简单易用的 API

## 支持的对齐方式

```typescript
type ImageCropPosition = 
  | 'left-top' | 'center-top' | 'right-top'
  | 'left-middle' | 'center-middle' | 'right-middle'
  | 'left-bottom' | 'center-bottom' | 'right-bottom'
```

## 基本使用

### 1. 插入图片并设置对齐方式

```typescript
import { ImageDrawer } from '../shape/drawers/ImageDrawer'

const imageDrawer = new ImageDrawer(board)

// 插入图片并设置为左上角对齐
const imageModel = await imageDrawer.insert('https://example.com/image.jpg', {
  cropPosition: 'left-top',
  x: 100,
  y: 100,
  width: 300,
  height: 200
})
```

### 2. 动态更改对齐方式

```typescript
// 更改为右下角对齐
imageModel.setCropPosition('right-bottom')

// 获取当前对齐方式
const currentPosition = imageModel.getCropPosition()
console.log('当前对齐方式:', currentPosition)
```

### 3. 使用示例工具类

```typescript
import { createImageCropExample } from '../examples/image-crop-example'

const example = createImageCropExample(board)

// 插入单个图片
const imageModel = await example.insertImage(
  'https://example.com/image.jpg', 
  'center-middle'
)

// 演示所有对齐方式（创建 3x3 网格）
const allImages = await example.demonstrateAll('https://example.com/image.jpg')

// 更改对齐方式
example.changePosition(imageModel, 'right-top')
```

## 对齐方式说明

| 位置 | 说明 | 效果 |
|------|------|------|
| `left-top` | 左上角对齐 | 图片左上角与容器左上角对齐 |
| `center-top` | 顶部居中对齐 | 图片顶部与容器顶部对齐，水平居中 |
| `right-top` | 右上角对齐 | 图片右上角与容器右上角对齐 |
| `left-middle` | 左侧居中对齐 | 图片左侧与容器左侧对齐，垂直居中 |
| `center-middle` | 完全居中对齐 | 图片在容器中完全居中（默认） |
| `right-middle` | 右侧居中对齐 | 图片右侧与容器右侧对齐，垂直居中 |
| `left-bottom` | 左下角对齐 | 图片左下角与容器左下角对齐 |
| `center-bottom` | 底部居中对齐 | 图片底部与容器底部对齐，水平居中 |
| `right-bottom` | 右下角对齐 | 图片右下角与容器右下角对齐 |

## 工作原理

1. **比例计算**: 根据目标尺寸和原图尺寸计算最佳裁剪区域
2. **位置计算**: 根据对齐方式计算裁剪起始位置
3. **自动应用**: 在图片变换时自动重新计算和应用裁剪
4. **比例保持**: 确保图片在任何变换后都保持正确的比例

## 技术实现

### ImageDrawer 类修改

- 添加了 `getCrop()` 方法来计算裁剪参数
- 添加了 `applyCrop()` 方法来应用裁剪
- 修改了 `insert()` 方法以支持对齐方式配置

### ImageModel 类修改

- 添加了 `cropPosition` 属性来存储当前对齐方式
- 添加了 `setCropPosition()` 和 `getCropPosition()` 方法
- 添加了 `onTransform()` 事件处理来保持裁剪比例
- 添加了内部的 `getCrop()` 和 `applyCropToSelf()` 方法

## 注意事项

1. **性能**: 变换时会重新计算裁剪，对于大量图片可能影响性能
2. **兼容性**: 确保图片已完全加载后再应用裁剪
3. **比例**: 裁剪会保持图片原始比例，可能会裁掉部分内容

## 与参考代码的对比

| 功能 | 参考代码 | 我们的实现 |
|------|----------|------------|
| 对齐方式 | ✅ 9种位置 | ✅ 9种位置 |
| 比例保持 | ✅ | ✅ |
| 变换时重新裁剪 | ✅ | ✅ |
| 集成度 | 独立实现 | 集成到现有架构 |
| 类型安全 | JavaScript | TypeScript |

## 未来改进

- [ ] 添加动画过渡效果
- [ ] 支持自定义对齐方式
- [ ] 优化性能，减少重复计算
- [ ] 添加可视化对齐方式选择器
