# 创建自定义形状

Pikaso 自带几种内置的[形状](/core/shapes)，可以满足大多数用例。

由于其强大的面向对象架构，可以轻松扩展 [ShapeDrawer](/api/classes/ShapeDrawer.html) 和 [ShapeModel](/api/classes/ShapeModel.html) 以创建任何自定义形状。

例如，让我们创建一个自定义的心形 ![heart](/shapes/heart.svg)。

需要扩展 [ShapeDrawer](/api/classes/ShapeDrawer.html) 和 [ShapeModel](/api/classes/ShapeModel.html) 以允许我们创建模型并将其注册到 Shapes 中。

## 步骤 1: 创建形状模型

让我们从创建 HeartModel 开始。由于我们将绘制一个 SVG 心形，我们的模型应该如下所示。

Pikaso 将处理其余部分，但您可以根据需要更改 [ShapeModel](/api/classes/ShapeModel.html) 的任何方法。

```ts
import { Konva, ShapeModel } from 'pikaso'

export class HeartModel extends ShapeModel<Konva.Path, Konva.PathConfig> {
  // 定义形状类型
  public get type() {
    return 'heart'
  }
}
```

如果需要，您可以覆盖更多方法，例如添加自定义的旋转逻辑：

```ts
import { Konva, rotateAroundCenter, ShapeModel } from 'pikaso'

export class HeartModel extends ShapeModel<Konva.Path, Konva.PathConfig> {
  public get type() {
    return 'heart'
  }

  // 自定义旋转方法
  public rotate(theta: number) {
    rotateAroundCenter(this.node, theta)

    this.board.events.emit('shape:rotate', {
      shapes: [this]
    })
  }
}
```

## 步骤 2: 创建形状绘制器

下一步是描述心形应如何在画布上绘制自身。

```ts
import { Konva, Board, ShapeDrawer, getPointsDistance } from 'pikaso'

import { HeartModel } from './HeartModel'

export class HeartDrawer extends ShapeDrawer<Konva.Path, Konva.PathConfig> {
  public node: Konva.Path | null = null

  constructor(board: Board) {
    super(board, 'Heart')
  }

  // 继承并覆盖 insert 方法
  public insert(config: Konva.PathConfig): HeartModel {
    return super.insert(config)
  }

  // 继承并覆盖 draw 方法
  public draw(config: Partial<Konva.PathConfig> = {}) {
    super.draw(config)
  }

  // 创建实际的心形
  protected createShape(config: Omit<Konva.PathConfig, 'data'>): HeartModel {
    this.node = new Konva.Path({
      fill: 'red', // 默认填充颜色为红色
      ...config,
      // 心形的 SVG 路径数据
      data: 'M12 21.593c-5.63-5.539-11-10.297-11-14.402 0-3.791 3.068-5.191 5.281-5.191 1.312 0 4.151.501 5.719 4.457 1.59-3.968 4.464-4.447 5.726-4.447 2.54 0 5.274 1.621 5.274 5.181 0 4.069-5.136 8.625-11 14.402'
    })

    return new HeartModel(this.board, this.node)
  }

  /**
   * 开始绘制心形
   */
  protected onStartDrawing() {
    super.onStartDrawing()

    if (!this.isDrawing) {
      return
    }

    this.createShape({
      x: this.startPoint.x,
      y: this.startPoint.y,
      scaleX: 0,
      scaleY: 0,
      ...this.config
    })
  }

  /**
   * 通过改变心形的缩放比例继续绘制
   */
  protected onDrawing(e: Konva.KonvaEventObject<MouseEvent>) {
    super.onDrawing(e)

    if (!this.node) {
      return
    }

    const point = this.board.stage.getPointerPosition()!
    const distance = getPointsDistance(point, this.getShapePosition())

    this.node.setAttrs({
      x: point.x - this.node.width(),
      scaleX: distance / 10,
      scaleY: distance / 10
    })
  }
}
```

## 步骤 3: 注册自定义形状

注册心形模型的最后一步:

```ts
import type { BaseShapes } from 'pikaso'

// 扩展基础形状接口以包含我们的心形
interface Shapes extends BaseShapes {
  heart: HeartDrawer
}

// 创建带有我们自定义形状的编辑器
const editor = new Pikaso<Shapes>(
  {
    container: <容器元素>,
    ...options
  },
  board => ({
    heart: new HeartDrawer(board)
  })
)
```

## 步骤 4: 使用自定义形状

现在我们可以像其他内置形状一样在画布上插入或绘制心形。

```ts
// 创建心形
editor.shapes.heart.insert({
  x: 100,
  y: 100,
  scale: {
    x: 6,
    y: 6
  }
})

// 开始绘制心形
editor.shapes.heart.draw()

// 为心形添加动画
const heart = editor.shapes.heart.insert({
  x: 100,
  y: 100,
  scale: {
    x: 6,
    y: 6
  }
});

heart.to({
  duration: 10, // 10 秒动画
  x: editor.board.stage.width() - heart.node.width()
});
```

## 更多自定义选项

### 自定义 SVG 路径

您可以使用不同的 SVG 路径创建各种形状：

```ts
protected createShape(config: Omit<Konva.PathConfig, 'data'>): CustomModel {
  this.node = new Konva.Path({
    ...config,
    // 星形的 SVG 路径
    data: 'M 100,10 L 130,90 L 210,90 L 140,140 L 170,220 L 100,170 L 30,220 L 60,140 L -10,90 L 70,90 Z'
  })

  return new CustomModel(this.board, this.node)
}
```

### 创建复合形状

您可以通过组合多个基本形状来创建更复杂的自定义形状：

```ts
protected createShape(config: Omit<Konva.PathConfig, 'data'>): CustomModel {
  // 创建一个组
  const group = new Konva.Group({
    x: config.x || 0,
    y: config.y || 0
  });
  
  // 添加圆形
  const circle = new Konva.Circle({
    radius: 50,
    fill: 'red',
    x: 0,
    y: 0
  });
  
  // 添加矩形
  const rect = new Konva.Rect({
    width: 100,
    height: 50,
    fill: 'blue',
    x: -50,
    y: 50
  });
  
  // 将形状添加到组
  group.add(circle);
  group.add(rect);
  
  // 将组添加到画板
  this.board.layer.add(group);
  
  // 创建并返回自定义模型
  return new CustomModel(this.board, group);
}
```

### 添加交互行为

您可以为自定义形状添加特定的交互行为：

```ts
// 在 CustomModel 类中
public addCustomBehavior() {
  this.node.on('mouseover', () => {
    // 鼠标悬停时改变颜色
    this.node.fill('yellow');
    this.board.layer.batchDraw();
  });
  
  this.node.on('mouseout', () => {
    // 鼠标离开时恢复颜色
    this.node.fill('red');
    this.board.layer.batchDraw();
  });
  
  this.node.on('click', () => {
    // 点击时执行特定动作
    this.animate();
  });
}

// 在 CustomDrawer 类中，调用添加行为
protected createShape(config: Omit<Konva.PathConfig, 'data'>): CustomModel {
  // 创建形状...
  
  const model = new CustomModel(this.board, this.node);
  model.addCustomBehavior();
  return model;
}
```

## 完整实现示例

下面是创建自定义箭头形状的完整示例：

### ArrowModel.ts
```ts
import { Konva, ShapeModel } from 'pikaso'

export class ArrowModel extends ShapeModel<Konva.Arrow, Konva.ArrowConfig> {
  public get type() {
    return 'customArrow'
  }
  
  // 自定义方法：改变箭头头部大小
  public setHeadSize(size: number) {
    this.node.pointerLength(size);
    this.node.pointerWidth(size);
    this.board.layer.batchDraw();
    return this;
  }

  // 自定义方法：设置虚线样式
  public setDash(dash: number[]) {
    this.node.dash(dash);
    this.board.layer.batchDraw();
    return this;
  }
}
```

### ArrowDrawer.ts
```ts
import { Konva, Board, ShapeDrawer } from 'pikaso'
import { ArrowModel } from './ArrowModel'

export class ArrowDrawer extends ShapeDrawer<Konva.Arrow, Konva.ArrowConfig> {
  public node: Konva.Arrow | null = null

  constructor(board: Board) {
    super(board, 'CustomArrow')
  }

  public insert(config: Konva.ArrowConfig): ArrowModel {
    return super.insert(config)
  }

  public draw(config: Partial<Konva.ArrowConfig> = {}) {
    super.draw(config)
  }

  protected createShape(config: Konva.ArrowConfig): ArrowModel {
    this.node = new Konva.Arrow({
      stroke: 'purple',
      strokeWidth: 3,
      pointerLength: 10,
      pointerWidth: 10,
      ...config
    })

    return new ArrowModel(this.board, this.node)
  }

  protected onStartDrawing() {
    super.onStartDrawing()

    if (!this.isDrawing) {
      return
    }

    this.createShape({
      points: [this.startPoint.x, this.startPoint.y, this.startPoint.x, this.startPoint.y],
      ...this.config
    })
  }

  protected onDrawing(e: Konva.KonvaEventObject<MouseEvent>) {
    super.onDrawing(e)

    if (!this.node) {
      return
    }

    const point = this.board.stage.getPointerPosition()!
    
    this.node.points([this.startPoint.x, this.startPoint.y, point.x, point.y])
  }
}
```

### 注册和使用
```ts
// 注册自定义箭头
interface Shapes extends BaseShapes {
  customArrow: ArrowDrawer
}

const editor = new Pikaso<Shapes>(
  {
    container: document.getElementById('editor'),
    width: 600,
    height: 400
  },
  board => ({
    customArrow: new ArrowDrawer(board)
  })
)

// 使用自定义箭头
const arrow = editor.shapes.customArrow.insert({
  points: [50, 50, 200, 100],
  stroke: 'blue',
  strokeWidth: 3
});

// 使用自定义方法
arrow.setHeadSize(15);
arrow.setDash([5, 5]);

// 开始交互式绘制
editor.shapes.customArrow.draw({
  stroke: 'green',
  strokeWidth: 2
});
