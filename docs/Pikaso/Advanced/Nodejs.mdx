# Node.js

<PERSON><PERSON><PERSON> 原生支持 Node.js 环境。在 Node.js 环境中使用 Pikaso 与在浏览器中使用类似。

> 注意：此功能自 v2.7.0 版本起可用。

## 安装

首先，您需要安装 `canvas` 包，这是 Pikaso 在 Node.js 环境中的依赖项：

```bash
npm install canvas
```

## 基本用法

```js
const { Pikaso } = require('pikaso')

const editor = new Pikas<PERSON>({
  width: 800,
  height: 600
})
```

## 完整示例

以下是在 Node.js 环境中使用 Pikaso 的完整示例：

```js
const fs = require('fs');
const { Pikaso } = require('pikaso');

// 创建 Pikaso 编辑器实例
const editor = new Pikaso({
  width: 800,
  height: 600
});

// 添加一些形状
editor.shapes.circle.insert({
  x: 400,
  y: 300,
  radius: 100,
  fill: 'red'
});

editor.shapes.rect.insert({
  x: 200,
  y: 200,
  width: 150,
  height: 150,
  fill: 'blue'
});

// 添加文本
editor.shapes.text.insert({
  x: 300,
  y: 100,
  text: 'Pikaso in Node.js',
  fontSize: 24,
  fill: 'black'
});

// 导出为图像
const dataURL = editor.export({
  mimeType: 'image/png',
  quality: 1
});

// 从 DataURL 中提取 base64 数据
// DataURL 格式: data:image/png;base64,<base64 data>
const base64Data = dataURL.split(',')[1];

// 将 base64 数据写入文件
fs.writeFileSync('pikaso-output.png', Buffer.from(base64Data, 'base64'));

console.log('图像已保存为 pikaso-output.png');
```

## 加载图像

您可以从本地文件系统加载图像：

```js
const fs = require('fs');
const { createCanvas, loadImage } = require('canvas');
const { Pikaso } = require('pikaso');

// 创建 Pikaso 编辑器实例
const editor = new Pikaso({
  width: 800,
  height: 600
});

async function main() {
  try {
    // 使用 node-canvas 加载图像
    const image = await loadImage('input-image.jpg');
    
    // 创建画布并绘制图像
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0);
    
    // 在 Pikaso 中使用图像
    editor.board.background.fill(canvas);
    
    // 添加一些效果或形状
    editor.shapes.circle.insert({
      x: 400,
      y: 300,
      radius: 100,
      fill: 'rgba(255, 0, 0, 0.5)'
    });
    
    // 导出处理后的图像
    const dataURL = editor.export();
    const base64Data = dataURL.split(',')[1];
    fs.writeFileSync('processed-image.png', Buffer.from(base64Data, 'base64'));
    
    console.log('处理后的图像已保存');
  } catch (error) {
    console.error('处理图像时出错:', error);
  }
}

main();
```

## 批处理多个图像

Pikaso 在 Node.js 环境中特别适合批处理多个图像：

```js
const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const { Pikaso } = require('pikaso');

// 创建 Pikaso 编辑器实例
const editor = new Pikaso({
  width: 800,
  height: 600
});

// 添加水印函数
function addWatermark(editor) {
  editor.shapes.text.insert({
    x: 20,
    y: 20,
    text: '© 2025 My Company',
    fontSize: 24,
    fill: 'rgba(255, 255, 255, 0.7)',
    stroke: 'rgba(0, 0, 0, 0.3)',
    strokeWidth: 1
  });
}

// 批量处理图像
async function processImages(inputDir, outputDir) {
  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // 获取输入目录中的所有图像文件
  const files = fs.readdirSync(inputDir).filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.jpg', '.jpeg', '.png'].includes(ext);
  });
  
  console.log(`找到 ${files.length} 个图像文件需要处理`);
  
  // 处理每个图像
  for (const file of files) {
    try {
      const inputPath = path.join(inputDir, file);
      const outputPath = path.join(outputDir, `watermarked-${file}`);
      
      console.log(`处理: ${file}`);
      
      // 加载图像
      const image = await loadImage(inputPath);
      
      // 重置编辑器大小以匹配图像
      editor.board.stage.width(image.width);
      editor.board.stage.height(image.height);
      
      // 清除之前的内容
      editor.board.shapes.forEach(shape => shape.remove());
      editor.board.background.clear();
      
      // 设置图像作为背景
      const canvas = createCanvas(image.width, image.height);
      const ctx = canvas.getContext('2d');
      ctx.drawImage(image, 0, 0);
      editor.board.background.fill(canvas);
      
      // 添加水印
      addWatermark(editor);
      
      // 导出图像
      const dataURL = editor.export();
      const base64Data = dataURL.split(',')[1];
      fs.writeFileSync(outputPath, Buffer.from(base64Data, 'base64'));
      
      console.log(`已保存: ${outputPath}`);
      
    } catch (error) {
      console.error(`处理 ${file} 时出错:`, error);
    }
  }
  
  console.log('所有图像处理完成');
}

// 执行批处理
processImages('./input-images', './output-images');
```

## 注意事项与限制

1. **依赖项**: 确保安装了 `canvas` 包，这是 Pikaso 在 Node.js 环境中的必要依赖。

2. **系统要求**: `canvas` 包可能需要额外的系统库。如果安装时遇到问题，请参考 [node-canvas 安装指南](https://github.com/Automattic/node-canvas#installation)。

3. **内存使用**: 处理大图像时，请注意内存使用情况，特别是在批处理多个图像时。

4. **浏览器特定 API**: 某些依赖于浏览器特定 API 的功能（如文件拖放、剪贴板交互等）在 Node.js 环境中不可用。

5. **DOM 事件**: Node.js 环境中不存在 DOM 事件系统，因此某些事件处理可能需要不同的方法。

## 使用场景

在 Node.js 环境中使用 Pikaso 的常见场景包括：

- 服务器端图像处理和生成
- 批量为大量图像添加水印或应用滤镜
- 自动化图像转换和优化
- 图像合成和编辑作为构建过程的一部分
- 基于内容的图像分析和处理

更多高级用法和详细 API 文档，请参考 [Pikaso API 文档](/api/index.html)。
