# 安装

## NPM

Pikaso 同时提供 ES 模块和 CommonJS 捆绑包，可以轻松地使用流行的打包工具

```bash
npm install pikaso --save
```

## Yarn
```bash
yarn add pikaso
```

## \<script\> 标签

Pikaso 也支持 UMD 加载

```
<script src="https://unpkg.com/pikaso@latest/umd/pikaso.min.js" type="text/javascript"></script>
```

## 导入方式

安装完成后，可以根据需要以不同方式导入 Pikaso：

### 完整导入

```js
// ES 模块
import Pikaso from 'pikaso';

// 或者使用 CommonJS
const { Pikaso } = require('pikaso');
```

### 使用 UMD

如果通过 script 标签加载，Pikaso 将作为全局变量可用：

```
<script src="https://unpkg.com/pikaso@latest/umd/pikaso.min.js"></script>
<script>
  // 使用全局 Pikaso 对象
  const editor = new Pikaso({
    container: document.getElementById('editor')
  });
</script>
```

## 完整示例

以下是一个基本使用示例：

### HTML

```
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pikaso 示例</title>
  <style>
    #editor {
      width: 800px;
      height: 600px;
      margin: 0 auto;
      border: 1px solid #ccc;
    }
  </style>
</head>
<body>
  <div id="editor"></div>
  
  <!-- 使用 CDN -->
  <script src="https://unpkg.com/pikaso@latest/umd/pikaso.min.js"></script>
  <script>
    const editor = new Pikaso({
      container: document.getElementById('editor'),
      width: 800,
      height: 600
    });

    // 添加一个圆形
    editor.shapes.circle.insert({
      x: 400,
      y: 300,
      radius: 100,
      fill: 'red'
    });
  </script>
</body>
</html>
```

### 在 React 中使用

```jsx
import React, { useEffect, useRef } from 'react';
import Pikaso from 'pikaso';

function PikasoEditor() {
  const editorRef = useRef(null);
  const pikasoRef = useRef(null);

  useEffect(() => {
    if (editorRef.current && !pikasoRef.current) {
      // 初始化 Pikaso
      pikasoRef.current = new Pikaso({
        container: editorRef.current,
        width: 800,
        height: 600
      });

      // 添加一个圆形
      pikasoRef.current.shapes.circle.insert({
        x: 400,
        y: 300,
        radius: 100,
        fill: 'blue'
      });
    }

    // 清理函数
    return () => {
      if (pikasoRef.current) {
        // 如果需要，进行清理
        pikasoRef.current = null;
      }
    };
  }, []);

  return (
    <div>
      <div ref={editorRef} style={{ width: '800px', height: '600px', margin: '0 auto', border: '1px solid #ccc' }}></div>
    </div>
  );
}

export default PikasoEditor;
```

### 在 Vue 中使用

```vue
<template>
  <div>
    <div ref="editorRef" style="width: 800px; height: 600px; margin: 0 auto; border: 1px solid #ccc;"></div>
  </div>
</template>

<script>
import Pikaso from 'pikaso';

export default {
  name: 'PikasoEditor',
  data() {
    return {
      editor: null
    };
  },
  mounted() {
    this.initPikaso();
  },
  beforeDestroy() {
    // 清理资源
    this.editor = null;
  },
  methods: {
    initPikaso() {
      this.editor = new Pikaso({
        container: this.$refs.editorRef,
        width: 800,
        height: 600
      });

      // 添加一个矩形
      this.editor.shapes.rect.insert({
        x: 300,
        y: 200,
        width: 200,
        height: 150,
        fill: 'green'
      });
    }
  }
}
</script>
```

## TypeScript 支持

Pikaso 内置 TypeScript 类型定义，因此无需安装额外的类型包。您可以直接在 TypeScript 项目中使用：

```typescript
import Pikaso, { ShapeModel } from 'pikaso';

const editor = new Pikaso({
  container: document.getElementById('editor') as HTMLDivElement,
  width: 800,
  height: 600
});

// 使用泛型获取正确的类型
const circle: ShapeModel = editor.shapes.circle.insert({
  x: 400,
  y: 300,
  radius: 100,
  fill: 'red'
});
