# 实用工具函数

Pikaso 提供了一系列实用工具函数，帮助您处理常见的操作和计算。这些函数可以提高开发效率并简化代码。

## 几何与旋转

### convertDegreeToRadian

将角度值转换为弧度。

```ts
function convertDegreeToRadian(degree: number): number
```

**计算公式**: 
```
radian = (degree * PI) / 180
```

**示例**:
```js
import { convertDegreeToRadian } from 'pikaso';

// 将 90 度转换为弧度
const radians = convertDegreeToRadian(90); // 返回 Math.PI/2 (约 1.57)
```

### getRotateCenterPoint

根据给定的弧度角计算给定输入点的中心点。

```ts
function getRotateCenterPoint({ x, y }: Point, radian: number): Point
```

**示例**:
```js
import { getRotateCenterPoint, convertDegreeToRadian } from 'pikaso';

// 计算点 (100, 100) 旋转 45 度后的中心点
const point = { x: 100, y: 100 };
const radian = convertDegreeToRadian(45);
const centerPoint = getRotateCenterPoint(point, radian);
```

### getPointsDistance

计算两个给定点之间的距离。

```ts
function getPointsDistance(p1: Point, p2: Point): number
```

**示例**:
```js
import { getPointsDistance } from 'pikaso';

const point1 = { x: 0, y: 0 };
const point2 = { x: 3, y: 4 };
const distance = getPointsDistance(point1, point2); // 返回 5
```

### getRotatedPoint

计算给定点在指定角度旋转后的新位置。

```ts
function getRotatedPoint(point: Point, angle: number): Point
```

**更多信息**: [旋转点计算原理](https://drive.google.com/file/d/1yhi1vG9a_U0rPpz57jWEi8DQJemrtjr9/view?usp=sharing)

**示例**:
```js
import { getRotatedPoint } from 'pikaso';

const point = { x: 100, y: 0 };
const angle = Math.PI / 2; // 90 度（弧度表示）
const rotatedPoint = getRotatedPoint(point, angle); 
// 返回接近 { x: 0, y: 100 } 的点（受浮点数精度影响可能有微小差异）
```

### rotateAroundCenter

根据给定角度，围绕其中心旋转给定节点。

```ts
function rotateAroundCenter(node: Konva.Group | Konva.Shape | Konva.Layer | Konva.Stage, theta: number): void
```

**示例**:
```js
import { rotateAroundCenter, convertDegreeToRadian } from 'pikaso';

// 创建编辑器和形状
const editor = new Pikaso({ container: document.getElementById('editor') });
const circle = editor.shapes.circle.insert({ x: 100, y: 100, radius: 50, fill: 'red' });

// 围绕中心旋转圆形 45 度
const angle = convertDegreeToRadian(45);
rotateAroundCenter(circle.node, angle);
```

## 图像处理

### createImageFromUrl

下载并将 URL 转换为 Image 对象。

```ts
function createImageFromUrl(url: string): Promise<Konva.Image>
```

**示例**:
```js
import { createImageFromUrl } from 'pikaso';

// 从 URL 创建图像
async function loadImage() {
  try {
    const image = await createImageFromUrl('https://example.com/image.jpg');
    console.log('图像已加载，宽度:', image.width(), '高度:', image.height());
    return image;
  } catch (error) {
    console.error('加载图像失败:', error);
  }
}
```

### imageToDataUrl

将给定的文件转换为 base64 URL。

```ts
function imageToDataUrl(file: File): Promise<string>
```

**示例**:
```js
import { imageToDataUrl } from 'pikaso';

// 处理文件输入
document.getElementById('fileInput').addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      const dataUrl = await imageToDataUrl(file);
      console.log('图像已转换为 Data URL:', dataUrl.substring(0, 50) + '...');
      
      // 使用 dataUrl，例如在 img 元素中预览
      document.getElementById('preview').src = dataUrl;
    } catch (error) {
      console.error('转换图像失败:', error);
    }
  }
});
```

## 文本处理

### convertHtmlToText

将给定的 HTML 转换为纯文本。

```ts
function convertHtmlToText(html = ''): string
```

**示例**:
```js
import { convertHtmlToText } from 'pikaso';

const html = '<p>Hello <strong>World</strong>!</p>';
const text = convertHtmlToText(html); // 返回 "Hello World!"
```

## 对象操作

### mergeSettings

将 Pikaso 默认设置与给定设置合并。

```ts
function mergeSettings(settings: Settings): Settings
```

**示例**:
```js
import { mergeSettings } from 'pikaso';
import { defaultSettings } from 'pikaso/lib/settings';

// 合并自定义设置与默认设置
const customSettings = {
  selection: {
    interactive: false
  }
};

const mergedSettings = mergeSettings(customSettings);
console.log('合并后的设置:', mergedSettings);
```

### omit

创建一个对象，该对象由对象自身和继承的可枚举属性路径组成，这些路径未被省略。

```ts
function omit(object: object, keys: string[]): object
```

**示例**:
```js
import { omit } from 'pikaso';

const user = {
  id: 1,
  name: 'John',
  email: '<EMAIL>',
  password: 'secret'
};

// 创建一个不包含密码的用户对象
const safeUser = omit(user, ['password']);
console.log(safeUser); // { id: 1, name: 'John', email: '<EMAIL>' }
```

## 实际应用示例

### 计算并绘制两点之间的连线

```js
import { getPointsDistance, convertDegreeToRadian, getRotatedPoint } from 'pikaso';

// 创建编辑器
const editor = new Pikaso({ 
  container: document.getElementById('editor') 
});

// 创建两个点
const point1 = { x: 100, y: 100 };
const point2 = { x: 300, y: 200 };

// 绘制两个点
editor.shapes.circle.insert({
  x: point1.x,
  y: point1.y,
  radius: 5,
  fill: 'red'
});

editor.shapes.circle.insert({
  x: point2.x,
  y: point2.y,
  radius: 5,
  fill: 'blue'
});

// 计算两点之间的距离
const distance = getPointsDistance(point1, point2);

// 绘制连接两点的线
editor.shapes.line.insert({
  points: [point1.x, point1.y, point2.x, point2.y],
  stroke: 'black',
  strokeWidth: 2
});

// 在线的中点添加距离文本
const midPoint = {
  x: (point1.x + point2.x) / 2,
  y: (point1.y + point2.y) / 2
};

editor.shapes.text.insert({
  text: `${Math.round(distance)}px`,
  x: midPoint.x,
  y: midPoint.y,
  fill: 'green',
  fontSize: 14
});
```

### 创建图像上传和处理功能

```js
import { imageToDataUrl, createImageFromUrl } from 'pikaso';

// 创建编辑器
const editor = new Pikaso({ 
  container: document.getElementById('editor'),
  width: 800,
  height: 600
});

// 处理文件上传
document.getElementById('uploadButton').addEventListener('click', () => {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'image/*';
  
  fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      // 转换文件为 Data URL
      const dataUrl = await imageToDataUrl(file);
      
      // 从 Data URL 创建图像
      editor.loadFromUrl(dataUrl)
        .then(() => {
          console.log('图像已加载到编辑器');
        })
        .catch(error => {
          console.error('加载图像到编辑器失败:', error);
        });
    } catch (error) {
      console.error('处理图像失败:', error);
    }
  });
  
  fileInput.click();
});

// 添加水印示例
document.getElementById('addWatermark').addEventListener('click', () => {
  if (!editor.board.background.exists) {
    alert('请先上传一张图片');
    return;
  }
  
  editor.shapes.text.insert({
    text: '© 2025 My Company',
    x: 20,
    y: 20,
    fontSize: 20,
    fontStyle: 'bold',
    fill: 'rgba(255, 255, 255, 0.7)',
    stroke: 'rgba(0, 0, 0, 0.3)',
    strokeWidth: 1
  });
});

// 导出处理后的图像
document.getElementById('exportButton').addEventListener('click', () => {
  const dataUrl = editor.export({
    mimeType: 'image/png',
    quality: 1
  });
  
  const link = document.createElement('a');
  link.href = dataUrl;
  link.download = 'processed-image.png';
  link.click();
});
```

### 实现自定义旋转控件

```js
import { convertDegreeToRadian, rotateAroundCenter } from 'pikaso';

// 创建编辑器
const editor = new Pikaso({ 
  container: document.getElementById('editor') 
});

// 创建一个矩形
const rect = editor.shapes.rect.insert({
  x: 300,
  y: 200,
  width: 200,
  height: 100,
  fill: 'blue',
  draggable: true
});

// 创建一个旋转控制 UI
const rotationSlider = document.createElement('input');
rotationSlider.type = 'range';
rotationSlider.min = '0';
rotationSlider.max = '360';
rotationSlider.value = '0';
rotationSlider.style.width = '300px';
document.getElementById('controls').appendChild(rotationSlider);

// 添加旋转事件监听器
rotationSlider.addEventListener('input', (event) => {
  const degrees = parseInt(event.target.value);
  const radians = convertDegreeToRadian(degrees);
  
  // 围绕中心旋转矩形
  rotateAroundCenter(rect.node, radians);
  
  // 更新显示
  document.getElementById('angleDisplay').textContent = `${degrees}°`;
});
```

这些实用工具函数为 Pikaso 提供了额外的功能和灵活性，可以帮助您创建更复杂和高级的图像编辑应用。
