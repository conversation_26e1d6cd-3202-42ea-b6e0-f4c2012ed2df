# Flip

在处理 [Image](/api/classes/ImageModel.html) 或 [Shape](/api/classes/ShapeModel.html) 时，[Flip](/api/classes/Flip.html) 是一项允许您水平 [horizontal](/api/classes/Flip.html#horizontal) 或垂直 [vertical](/api/classes/Flip.html#vertical) 翻转图像或形状的功能。

[在维基百科上阅读更多](https://en.wikipedia.org/wiki/Flipped_image)

## 基本用法

```js
// 翻转画板，包括其上的所有活跃形状
editor.board.flip.vertical()
editor.board.flip.horizontal()

// 翻转背景
editor.board.background.image.flipX()
editor.board.background.image.flipY()

// 翻转选中的形状
editor.board.flip.vertical(editor.board.selection.shapes)
editor.board.flip.horizontal(editor.board.selection.shapes)

// 翻转单个形状
const circle = editor.shapes.circle.insert({
  fill: 'red',
  x: 100,
  y: 100,
  radius: 50
})

circle.flipX()
circle.flipY()

// 或者使用以下方式
editor.board.flip.vertical([circle])
editor.board.flip.horizontal([circle])
```

## 翻转示例

以下是一个完整的翻转示例，展示了如何翻转背景、整个画板和选中的形状：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function FlipExample() {
  const [ref, editor] = usePikaso({});

  useEffect(() => {
    const initialize = async () => {
      if (!editor) return;

      // 加载背景图像
      await editor.loadFromUrl(
        'https://images.unsplash.com/photo-1504194104404-433180773017?auto=format&fit=crop&w=1740&q=80'
      );

      // 添加一些形状
      editor.shapes.image.insert('/tiger.svg', {
        x: 1000,
        y: 200,
        cropWidth: 100
      });

      editor.shapes.polygon.insert({
        sides: 5,
        fillLinearGradientStartPoint: { x: -50, y: -50 },
        fillLinearGradientEndPoint: { x: 50, y: 50 },
        fillLinearGradientColorStops: [0, 'blue', 1, 'yellow'],
        radius: 200,
        x: 300,
        y: 400
      });
    };

    initialize();
  }, [editor]);

  // 翻转背景图像
  const flipBackgroundX = () => {
    if (!editor) return;
    editor.board.background.image.flipX();
  };

  const flipBackgroundY = () => {
    if (!editor) return;
    editor.board.background.image.flipY();
  };

  // 翻转整个画板
  const flipBoardHorizontal = () => {
    if (!editor) return;
    editor.flip.horizontal();
  };

  const flipBoardVertical = () => {
    if (!editor) return;
    editor.flip.vertical();
  };

  // 翻转选中的形状
  const flipSelectedShapesHorizontal = () => {
    if (!editor) return;
    editor.flip.horizontal(editor.board.selection.shapes);
  };

  const flipSelectedShapesVertical = () => {
    if (!editor) return;
    editor.flip.vertical(editor.board.selection.shapes);
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          width: '600px',
          height: '400px'
        }}
      />
      
      <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'center', gap: '10px' }}>
        <div>
          <h3>背景图像</h3>
          <button onClick={flipBackgroundX}>水平翻转</button>
          <button onClick={flipBackgroundY}>垂直翻转</button>
        </div>
        
        <div>
          <h3>整个画板</h3>
          <button onClick={flipBoardHorizontal}>水平翻转</button>
          <button onClick={flipBoardVertical}>垂直翻转</button>
        </div>
        
        <div>
          <h3>选中的形状</h3>
          <button onClick={flipSelectedShapesHorizontal}>水平翻转</button>
          <button onClick={flipSelectedShapesVertical}>垂直翻转</button>
        </div>
      </div>
    </div>
  );
}
```

## API 参考

### 画板翻转

```js
// 水平翻转整个画板
editor.board.flip.horizontal();
// 或
editor.flip.horizontal();

// 垂直翻转整个画板
editor.board.flip.vertical();
// 或
editor.flip.vertical();
```

### 背景翻转

```js
// 水平翻转背景图像
editor.board.background.image.flipX();

// 垂直翻转背景图像
editor.board.background.image.flipY();
```

### 形状翻转

您可以使用形状的实例方法进行翻转：

```js
// 创建一个形状
const shape = editor.shapes.rect.insert({
  x: 100,
  y: 100,
  width: 200,
  height: 100,
  fill: 'blue'
});

// 水平翻转形状
shape.flipX();

// 垂直翻转形状
shape.flipY();
```

或者使用画板的 flip 方法并传入形状列表：

```js
// 水平翻转指定形状
editor.board.flip.horizontal([shape1, shape2]);

// 垂直翻转指定形状
editor.board.flip.vertical([shape1, shape2]);
```

### 选中形状翻转

```js
// 水平翻转所有选中的形状
editor.board.flip.horizontal(editor.board.selection.shapes);

// 垂直翻转所有选中的形状
editor.board.flip.vertical(editor.board.selection.shapes);
```

## 注意事项

1. 翻转操作会改变形状的坐标和变换属性。
2. 对于文本形状，水平翻转可能导致文本显示反向，这通常不是期望的结果。
3. 对于具有不对称滤镜或效果的形状，翻转可能导致视觉效果的变化。
4. 翻转整个画板会翻转所有的形状，包括背景图像。

## 组合翻转

您可以组合水平和垂直翻转来达到旋转 180 度的效果：

```js
// 同时进行水平和垂直翻转（相当于旋转 180 度）
shape.flipX();
shape.flipY();

// 或者使用画板方法
editor.flip.horizontal();
editor.flip.vertical();
```

## 翻转与旋转的区别

翻转会镜像反转形状，而旋转则是按照一定角度移动形状：

- 翻转：形状的镜像反转
- 旋转：形状绕中心点的角度变化

例如，水平翻转会将形状的左侧变为右侧，而 180 度旋转则会使形状上下颠倒。
