# Label

Label（标签）是一种特殊的 [Text](/api/classes/Text.html) 和 [Shape Model](/api/classes/ShapeModel.html)，它包含了 [Text](/core/shapes/text) 和 [Tag](https://konvajs.org/api/Konva.Tag.html)。

Label 具有内置功能，如内联编辑和自动转换。

## 基本用法

```js
// 创建一个新标签
const myLabel = editor.shapes.label.insert({
  container: {
    x: 40,
    y: 100
  },
  tag: {
    fill: '#262626'
  },
  text: {
    text: 'Pikaso Rocks',
    fill: '#00ff00',
    fontSize: 40
  }
})

// 更新标签的背景
mylabel.updateTag({
  fill: '#fff',
  cornerRadius: [2, 0, 2, 0] // 或简单地设置 cornerRadius: 2
})

// 更新标签的文本
mylabel.updateText({
  fill: '#ff0000',
  fontSize: 30,
  fontFamily: 'Arial'
})

// 可以使用所有 ShapeModel 的方法和属性
mylabel.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 10
  }
})
```

## 交互式示例

以下是一个交互式标签示例，展示如何创建标签并调整其属性：

```jsx
import { useEffect } from 'react'
import { LabelModel } from 'pikaso'
import usePikaso from './hooks/use-pikaso'

function LabelExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  })

  useEffect(() => {
    if (editor) {
      editor.shapes.label.insert({
        container: {
          x: 300,
          y: 100
        },
        tag: {
          fill: '#262626',
          cornerRadius: 0
        },
        text: {
          text: 'Pikaso Rocks',
          fill: '#00ff00',
          fontSize: 60
        }
      })
    }
  }, [editor])

  // 字体颜色改变处理函数
  const handleChangeColor = (fill) => {
    const shape = editor?.board.shapes.find(() => true)
    shape?.updateText({ fill })
  }

  // 背景颜色改变处理函数
  const handleChangeBackgroundColor = (fill) => {
    const shape = editor?.board.shapes.find(() => true)
    shape?.updateTag({ fill })
  }

  // 字体大小改变处理函数
  const handleChangeFontSize = (fontSize) => {
    const shape = editor?.board.shapes.find(() => true)
    shape?.updateText({ fontSize })
  }

  // 边框圆角改变处理函数
  const handleChangeBorderRadius = (radius) => {
    const shape = editor?.board.shapes.find(() => true)
    shape?.updateTag({ cornerRadius: radius })
  }

  return (
    <div
      ref={ref}
      style={{
        margin: '0 auto',
        background: '#CBC3E3',
        width: '100%',
        height: '300px'
      }}
    />
  )
}
```

## 属性

Label 组件支持以下属性配置：

### container
设置标签容器的位置和其他属性
- `x`: 横坐标位置
- `y`: 纵坐标位置

### tag
设置标签背景的属性
- `fill`: 背景颜色
- `cornerRadius`: 边角圆角半径，可以是单个数值或四个值的数组 [左上, 右上, 右下, 左下]

### text
设置标签文本的属性
- `text`: 文本内容
- `fill`: 文本颜色
- `fontSize`: 字体大小
- `fontFamily`: 字体

## 方法

### updateTag(options)
更新标签背景的属性

### updateText(options)
更新标签文本的属性

Label 还继承了 ShapeModel 的所有方法，比如 `addFilter()`, `remove()` 等。
