# Drawing

[Shape Drawer](/api/classes/ShapeDrawer.html) 和 [Shape Model](/api/classes/ShapeModel.html) 是 Pikaso 形状的基本组件。

[Shape Drawer](/api/classes/ShapeDrawer.html) 表示一个形状在被创建时的行为方式，而 [Shape Model](/api/classes/ShapeModel.html) 表示它在创建后的行为方式。

[insert](/api/classes/ShapeDrawer.html#insert)、[draw](/api/classes/ShapeDrawer.html#draw) 和 [stopDrawing](/api/classes/ShapeDrawer.html#stopDrawing) 是 [Shape Drawer](/api/classes/ShapeDrawer.html) 最重要的方法。

## 基本用法

```js
// 插入一个圆形
editor.shapes.circle.insert({
  /* 配置 */
})

// 开始绘制一个圆形
editor.shapes.circle.draw({
  fill: 'blue'
})

// 停止绘制圆形
editor.shapes.circle.stopDrawing()

// 开始绘制一个多边形
editor.shapes.polygon.draw({
  fill: 'red',
  sides: 5
})
```

## 绘图模式

Pikaso 支持多种绘图模式，包括：

- Circle（圆形）
- Ellipse（椭圆形）
- Rectangle（矩形）
- Polygon（多边形）
- Triangle（三角形）
- Arrow（箭头）
- Line（直线）
- Pencil（自由绘制）

### 示例

以下是一个完整的绘图示例，展示如何使用不同的绘图工具：

```jsx
import { useState, useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function DrawingExample() {
  // 当前选中的绘图工具
  const [activeDrawing, setActiveDrawing] = useState('pencil');
  
  // 绘图工具配置
  const drawings = {
    circle: {
      title: '圆形',
      config: {
        fill: getRandomColor(),
        stroke: getRandomColor()
      }
    },
    ellipse: {
      title: '椭圆',
      config: {
        fill: getRandomColor(),
        stroke: getRandomColor()
      }
    },
    rect: {
      title: '矩形',
      config: {
        fill: getRandomColor(),
        stroke: getRandomColor()
      }
    },
    polygon: {
      title: '多边形',
      config: {
        sides: 5,
        fill: getRandomColor(),
        stroke: getRandomColor()
      }
    },
    triangle: {
      title: '三角形',
      config: {
        fill: getRandomColor(),
        stroke: getRandomColor()
      }
    },
    arrow: {
      title: '箭头',
      config: {
        stroke: getRandomColor(),
        strokeWidth: 15
      }
    },
    line: {
      title: '直线',
      config: {
        stroke: getRandomColor(),
        strokeWidth: 15
      }
    },
    pencil: {
      title: '自由绘制',
      config: {
        stroke: getRandomColor(),
        strokeWidth: 15
      }
    }
  };

  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  // 生成随机颜色
  function getRandomColor() {
    return `#${Math.floor(Math.random()*16777215).toString(16)}`;
  }

  // 初始化自由绘制工具
  useEffect(() => {
    if (editor) {
      editor.shapes.pencil.draw({
        stroke: 'blue',
        strokeWidth: 15
      });
    }
  }, [editor]);

  // 切换绘图工具
  const handleChangeDrawer = (shape) => {
    if (!editor) {
      return;
    }

    // 停止当前的绘制
    editor.shapes[activeDrawing].stopDrawing();
    
    // 开始新的绘制模式
    editor.shapes[shape].draw(drawings[shape].config);
    
    // 更新当前工具
    setActiveDrawing(shape);
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '350px'
        }}
      />
      
      <div style={{ marginTop: '10px' }}>
        <div>当前工具: {drawings[activeDrawing].title}</div>
        <div>
          {Object.entries(drawings).map(([name, shape]) => (
            <button 
              key={name}
              onClick={() => handleChangeDrawer(name)}
              style={{
                margin: '5px',
                backgroundColor: activeDrawing === name ? '#ddd' : '#fff'
              }}
            >
              {shape.title}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
```

## API 参考

### insert 方法

`insert` 方法用于直接插入一个形状，而无需交互式绘制：

```js
// 插入一个圆形
const circle = editor.shapes.circle.insert({
  x: 100,
  y: 100,
  radius: 50,
  fill: 'red'
});
```

对于不同类型的形状，配置参数会有所不同：

#### Circle（圆形）
```js
editor.shapes.circle.insert({
  x: 100,
  y: 100,
  radius: 50,
  fill: 'blue',
  stroke: 'black',
  strokeWidth: 2
});
```

#### Rectangle（矩形）
```js
editor.shapes.rect.insert({
  x: 100,
  y: 100,
  width: 200,
  height: 100,
  fill: 'green',
  cornerRadius: 10
});
```

#### Polygon（多边形）
```js
editor.shapes.polygon.insert({
  x: 200,
  y: 200,
  sides: 6,
  radius: 70,
  fill: 'purple'
});
```

### draw 方法

`draw` 方法开启交互式绘制模式，允许用户通过点击和拖动来创建形状：

```js
// 开始绘制一个红色的三角形
editor.shapes.triangle.draw({
  fill: 'red',
  stroke: 'black'
});
```

### stopDrawing 方法

`stopDrawing` 方法结束当前的绘制模式：

```js
// 停止当前的绘制
editor.shapes.triangle.stopDrawing();
```

## 创建自定义形状

要了解如何扩展 [Shape Model](/api/classes/ShapeModel.html) 和 [Shape Drawer](/api/classes/ShapeDrawer.html) 类来创建自定义形状，请查看 [tutorials/create-custom-shapes](/tutorials/create-custom-shapes)。

自定义形状允许您创建特殊的绘图工具，如心形、星形或任何其他特定需求的形状。
