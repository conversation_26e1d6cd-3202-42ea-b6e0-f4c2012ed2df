# Selection

选择组件（Selection）提供了对画板中创建的 [shapes](/api/classes/ShapeModel.html) 的视觉和程序化控制。

## 基本配置

```js
// 配置选择功能
const editor = new Pikaso({
  container: <element>,
  selection: {
    interactive: true, // 启用或禁用视觉选择
    keyboard: {
      enabled: true, // 启用或禁用键盘快捷键
      movingSpaces: 5, // 使用箭头键移动的像素数
      map: {
        delete: ['Backspace', 'Delete'], // 删除选中的形状
        moveLeft: ['ArrowLeft'], // 向左移动选中的形状
        moveRight: ['ArrowRight'], // 向右移动选中的形状
        moveUp: ['ArrowUp'], // 向上移动选中的形状
        moveDown: ['ArrowDown'], // 向下移动选中的形状
        deselect: ['Escape'] // 取消选择
      }
    },
    transformer: {
      borderStroke: '#fff', // 选择边框颜色
      borderStrokeWidth: 3, // 选择边框宽度
      anchorSize: 15, // 变换锚点大小
      anchorFill: '#fff', // 变换锚点填充颜色
      anchorStroke: '#fff', // 变换锚点边框颜色
      anchorStrokeWidth: 1, // 变换锚点边框宽度
      anchorCornerRadius: 30, // 变换锚点圆角半径
      borderDash: [0, 0] // 选择边框虚线样式
    },
    zone: {
      fill: 'rgba(105, 105, 105, 0.7)', // 多选区域填充颜色
      stroke: '#dbdbdb' // 多选区域边框颜色
    }
  }
})
```

## 基本用法

```js
// 创建一个圆形
const shape = editor.shapes.circle.insert({ /* 配置 */ })

// 选择创建的形状
shape.select() // 或 editor.board.selection.add([shape])

// 取消选择该形状
shape.deselect()
```

## 选择操作

```js
// 切换形状的选择状态
editor.board.selection.toggle(shape)

// 选择画板中的所有形状
editor.board.selection.selectAll()

// 取消选择画板中的所有形状
editor.board.selection.deselectAll()

// 选择多个形状
editor.board.selection.multi([shape, ...更多形状])

// 删除所有选中的形状
editor.board.selection.delete()

// 移动选中的形状
editor.board.selection.moveX(100) // X轴移动100像素
editor.board.selection.moveY(100) // Y轴移动100像素

// 为选中的形状添加滤镜
editor.board.selection.addFilter({
  name: 'blur',
  options: {
    blurRadius: 10
  }
})
```

## 选择示例

以下是一个完整的选择交互示例，演示如何使用各种选择功能：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function SelectionExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;
    
    // 初始化时添加一些形状
    editor.shapes.image.insert('/tiger.svg', {
      x: 100,
      y: 100
    });

    editor.shapes.circle.insert({
      fill: 'red',
      radius: 50,
      x: 500,
      y: 100
    });

    editor.shapes.label.insert({
      container: {
        x: 600,
        y: 300
      },
      tag: {
        fill: 'transparent',
        cornerRadius: 0
      },
      text: {
        text: "Pikaso is great, isn't it?",
        fontSize: 40,
        fontWeight: 'bold',
        fill: 'purple'
      }
    });
  }, [editor]);

  // 选择图像并移动
  const selectImageAndMove = () => {
    if (!editor) return;
    
    // 先取消所有选择
    editor.board.selection.deselectAll();

    // 找到并选择图像
    const shape = editor.board.shapes.find(shape => {
      return shape.node.getClassName() === 'Image';
    });

    if (shape) {
      editor.board.selection.toggle(shape);
      
      // 移动选中的图像
      editor.board.selection.moveX(100);
      editor.board.selection.moveY(100);
    }
  };

  // 切换圆形的选择状态
  const toggleCircle = () => {
    if (!editor) return;
    
    const shape = editor.board.shapes.find(shape => {
      return shape.node.getClassName() === 'Circle';
    });

    if (shape) {
      editor.board.selection.toggle(shape);
    }
  };

  // 选择标签
  const selectLabel = () => {
    if (!editor) return;
    
    // 先取消所有选择
    editor.board.selection.deselectAll();

    // 使用 find 方法选择标签
    editor.board.selection.find(shape => {
      return shape.node.getClassName() === 'Label';
    });
  };

  // 选择所有形状
  const selectAll = () => {
    if (!editor) return;
    editor.selection.selectAll();
  };

  // 取消选择所有形状
  const deselectAll = () => {
    if (!editor) return;
    editor.selection.deselectAll();
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '500px'
        }}
      />
      
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <button onClick={selectImageAndMove} style={{ marginRight: '5px' }}>
            选择图像并移动
          </button>
          <button onClick={toggleCircle} style={{ marginRight: '5px' }}>
            切换圆形选择
          </button>
          <button onClick={selectLabel} style={{ marginRight: '5px' }}>
            选择标签
          </button>
        </div>
        
        <div>
          <button onClick={selectAll} style={{ marginRight: '5px' }}>
            全选
          </button>
          <button onClick={deselectAll}>
            取消全选
          </button>
        </div>
      </div>
    </div>
  );
}
```

## 选择方法

### 基本方法

#### 单个形状的选择与取消选择

```js
// 直接在形状实例上使用
shape.select(); // 选择形状
shape.deselect(); // 取消选择形状

// 或使用选择模块
editor.board.selection.select(shape);
editor.board.selection.deselect(shape);
```

#### 多个形状的选择

```js
// 多选 - 同时选择多个形状
editor.board.selection.multi([shape1, shape2, shape3]);

// 累加选择 - 保留当前选择并添加新的形状
editor.board.selection.select(shape, true); // 第二个参数为 true 表示累加选择
```

#### 全选与取消全选

```js
// 选择所有形状
editor.board.selection.selectAll();

// 取消所有选择
editor.board.selection.deselectAll();
```

### 查询方法

#### 检查形状是否被选择

```js
// 检查形状是否被选择
const isSelected = shape.isSelected();
console.log(`形状是否被选择: ${isSelected}`);

// 获取所有选中的形状
const selectedShapes = editor.board.selection.shapes;
console.log(`已选择 ${selectedShapes.length} 个形状`);
```

#### 查找并选择形状

```js
// 查找并选择符合条件的形状
editor.board.selection.find(shape => {
  // 返回 true 的形状将被选中
  return shape.node.getClassName() === 'Circle';
});

// 查找但不选择
const foundShape = editor.board.shapes.find(shape => {
  return shape.fill() === 'red';
});
```

### 操作选中的形状

#### 移动选中的形状

```js
// 水平移动选中的形状
editor.board.selection.moveX(100);

// 垂直移动选中的形状
editor.board.selection.moveY(-50);

// 同时在两个方向移动
editor.board.selection.move({
  x: 100,
  y: -50
});
```

#### 删除选中的形状

```js
// 删除所有选中的形状
editor.board.selection.delete();
```

#### 对选中的形状应用滤镜

```js
// 为选中的形状添加滤镜
editor.board.selection.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 5
  }
});

// 从选中的形状移除滤镜
editor.board.selection.removeFilter({
  name: 'Blur'
});
```

#### 对选中的形状应用变换

```js
// 缩放选中的形状
editor.board.selection.scale({
  x: 1.5,
  y: 1.5
});

// 旋转选中的形状
editor.board.selection.rotate(45); // 旋转45度
```

## 自定义选择交互

您可以自定义选择的视觉外观和交互行为：

```js
// 创建 Pikaso 实例时定义选择样式
const editor = new Pikaso({
  container: document.getElementById('editor'),
  selection: {
    // 自定义选择变换器样式
    transformer: {
      borderStroke: 'blue', // 选择边框颜色
      borderStrokeWidth: 2, // 选择边框宽度
      anchorFill: 'white', // 锚点填充颜色
      anchorStroke: 'blue', // 锚点边框颜色
      anchorSize: 8, // 锚点大小
      anchorCornerRadius: 0 // 锚点圆角半径
    },
    // 自定义多选区域样式
    zone: {
      fill: 'rgba(0, 0, 255, 0.3)',
      stroke: 'blue'
    },
    // 自定义键盘交互
    keyboard: {
      enabled: true,
      movingSpaces: 10, // 每次按键移动10像素
      map: {
        delete: ['Delete'], // 只使用 Delete 键删除
        // 可以自定义其他键位映射
      }
    }
  }
});
```

## 选择事件

您可以监听选择相关的事件：

```js
// 监听形状选择事件
editor.on('shape:select', (data) => {
  console.log('形状被选中:', data);
});

// 监听形状取消选择事件
editor.on('shape:deselect', (data) => {
  console.log('形状取消选中:', data);
});

// 监听选择变更事件
editor.on('selection:change', (data) => {
  console.log('选择已更改:', data);
  console.log('当前选中的形状数量:', data.shapes.length);
});
```

## 键盘交互

默认情况下，选择支持以下键盘交互：

- **删除键**（Backspace 或 Delete）：删除选中的形状
- **方向键**：移动选中的形状
- **Esc 键**：取消选择

您可以在创建 Pikaso 实例时自定义这些键盘映射，如前面的示例所示。
