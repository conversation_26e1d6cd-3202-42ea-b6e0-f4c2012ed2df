# Import / Export

一个 [board workspace](/api/classes/Board.html) 可以被导出为 [图像](/api/classes/Export.html#toImage) 或 [JSON](https://en.wikipedia.org/wiki/JSON) 文件。

导出 [JSON](/api/classes/Export.html#toJson) 后，可以再次导入。

当需要将画板的当前状态存储在数据库中或导入现成的模板时，这非常有用。

## 基本用法

```js
// 导出为图像
editor.export.toImage({
  /* 导出配置 */
})

// 导出为 JSON
editor.export.toJson()

// 导入 JSON
editor.load('<json 字符串>') // 或 editor.import.json('<已解析的 JSON>')
```

## 导出图像

导出为图像功能允许您将画板转换为图像格式。您可以通过以下方式实现：

```jsx
// 导出为图像功能示例
function handleExportImage(editor) {
  if (!editor) {
    return;
  }

  // 通过配置 pixelRatio 可以提高导出图像的质量
  const image = editor.export.toImage({
    pixelRatio: 3
  });
  
  // image 是一个 base64 编码的 Data URL
  // 可以直接用于 <img> 标签的 src 属性
  return image;
}
```

### 导出图像配置选项

导出图像时可以使用以下配置选项：

- `pixelRatio`: 设置导出图像的像素比。较高的值会产生更高质量的图像，但文件大小也会增加。
- `mimeType`: 指定导出图像的 MIME 类型，例如 "image/png"（默认）、"image/jpeg" 等。
- `quality`: 当 mimeType 为 "image/jpeg" 时使用，指定 JPEG 图像的质量（0 到 1 之间）。

## 导出 JSON

导出为 JSON 功能允许您将画板的完整状态保存为 JSON 格式，便于后续导入或存储：

```jsx
// 导出为 JSON 功能示例
function handleExportJson(editor) {
  if (!editor) {
    return;
  }

  // 获取画板的 JSON 表示
  const json = editor.export.toJson();
  
  // 转换为字符串以便保存或传输
  return JSON.stringify(json);
}
```

导出的 JSON 包含画板上所有对象的完整信息，包括：
- 所有形状及其属性
- 图像及其位置和尺寸
- 文本内容和样式
- 组和层级结构
- 滤镜和效果

## 导入 JSON

导入 JSON 功能允许您从之前导出的 JSON 中恢复画板状态：

```jsx
// 导入 JSON 功能示例
function handleImport(editor, jsonString) {
  if (!editor) {
    return;
  }

  try {
    // 验证 JSON 是否有效
    JSON.parse(jsonString);
    
    // 清除当前画板
    editor.reset();
    
    // 加载新的内容
    editor.load(jsonString);
    
    return true;
  } catch (e) {
    console.error('无效的 JSON 输入', e);
    return false;
  }
}
```

## 完整示例

以下是一个完整的导入/导出功能实现示例：

```jsx
import { useEffect } from 'react';
import { Button } from '@mui/material';

function ImportExportExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });
  
  // 初始化画板内容
  useEffect(() => {
    if (!editor) return;
    
    editor.shapes.image.insert('/tiger.svg', {
      x: 100,
      y: 100
    });

    editor.shapes.circle.insert({
      fill: 'red',
      radius: 50,
      x: 500,
      y: 100
    });

    editor.shapes.label.insert({
      container: {
        x: 600,
        y: 300
      },
      tag: {
        fill: 'transparent',
        cornerRadius: 0
      },
      text: {
        text: "Pikaso is great, isn't it?",
        fontSize: 40,
        fontWeight: 'bold',
        fill: 'purple'
      }
    });
  }, [editor]);

  // 添加随机圆形
  const handleAddCircle = () => {
    if (!editor) return;
    
    editor.shapes.circle.insert({
      radius: Math.random() * 40 + 40, // 40 to 80
      fill: `#${Math.floor(Math.random() * 16777215).toString(16)}`, // 随机颜色
      x: Math.random() * (editor.board.stage.width() - 50),
      y: Math.random() * (editor.board.stage.height() - 50)
    });
  };
  
  // 导出为图像
  const handleExportImage = () => {
    if (!editor) return;
    
    const image = editor.export.toImage({
      pixelRatio: 3
    });
    
    // 这里可以展示图像或提供下载
    console.log('导出的图像:', image);
  };
  
  // 导出为 JSON
  const handleExportJson = () => {
    if (!editor) return;
    
    const json = editor.export.toJson();
    const jsonString = JSON.stringify(json);
    
    // 这里可以展示 JSON 或提供下载
    console.log('导出的 JSON:', jsonString);
  };
  
  // 导入 JSON
  const handleImport = (jsonString) => {
    if (!editor) return;
    
    try {
      JSON.parse(jsonString);
      
      editor.reset();
      setTimeout(() => {
        editor.load(jsonString);
      }, 1000);
      
      return true;
    } catch (e) {
      console.error('无效的 JSON 输入', e);
      return false;
    }
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '500px'
        }}
      />
      
      <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
        <div>
          <Button onClick={() => editor?.reset()}>
            重置画板
          </Button>
          <Button onClick={handleAddCircle}>
            添加圆形
          </Button>
        </div>
        
        <div>
          <Button onClick={handleExportImage}>
            导出图像
          </Button>
          <Button onClick={handleExportJson}>
            导出 JSON
          </Button>
          <Button onClick={() => handleImport(prompt('请输入 JSON:'))}>
            导入 JSON
          </Button>
        </div>
      </div>
    </div>
  );
}
```

## 高级用例

### 服务器存储与恢复

您可以使用导入/导出功能将用户的工作保存到服务器，并在需要时恢复：

```jsx
// 保存到服务器
async function saveToServer(editor) {
  const json = editor.export.toJson();
  
  try {
    const response = await fetch('/api/save-board', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: json }),
    });
    
    return await response.json();
  } catch (error) {
    console.error('保存失败:', error);
  }
}

// 从服务器加载
async function loadFromServer(editor, boardId) {
  try {
    const response = await fetch(`/api/load-board/${boardId}`);
    const data = await response.json();
    
    editor.reset();
    editor.load(JSON.stringify(data.boardData));
  } catch (error) {
    console.error('加载失败:', error);
  }
}
```

### 模板系统

您可以使用导入功能创建模板系统，让用户从预设模板开始：

```jsx
// 模板集合
const templates = {
  basic: {/* 基础模板 JSON */},
  professional: {/* 专业模板 JSON */},
  creative: {/* 创意模板 JSON */}
};

// 应用模板
function applyTemplate(editor, templateName) {
  const template = templates[templateName];
  
  if (template) {
    editor.reset();
    editor.load(JSON.stringify(template));
  }
}
