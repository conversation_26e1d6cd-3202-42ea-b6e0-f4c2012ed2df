# Cropper

Cropper（裁剪器）组件提供了多种裁剪功能，包括**灵活矩形裁剪**、**灵活圆形裁剪**、**固定矩形裁剪**和**固定圆形裁剪**。

所有的裁剪类型都是完全可定制的。

## 快速开始

```js
// 启动裁剪器
editor.cropper.start({
  // 裁剪器配置选项
  circular: false, // 是否为圆形裁剪 
  fixed: false, // 是否为固定尺寸裁剪
  keepRatio: true, // 保持比例
  aspectRatio: 1, // 宽高比（仅在 keepRatio 为 true 时有效）
  // ...其他配置
});

// 停止裁剪器
editor.cropper.stop();

// 获取裁剪结果
const result = editor.cropper.getCroppedImage();
```

## 裁剪类型

Cropper 组件支持四种基本的裁剪类型：

### 灵活矩形裁剪 (Flexible Rectangular)

这是最基本的裁剪类型，允许用户自由调整裁剪区域的位置和大小。

```js
editor.cropper.start({
  circular: false, // 矩形裁剪
  fixed: false, // 灵活尺寸
  minWidth: 50, // 最小宽度
  minHeight: 50 // 最小高度
});
```

### 灵活圆形裁剪 (Flexible Circular)

与灵活矩形裁剪类似，但裁剪区域是圆形的。

```js
editor.cropper.start({
  circular: true, // 圆形裁剪
  fixed: false, // 灵活尺寸
  minWidth: 50, // 最小直径
  minHeight: 50 // 最小直径（将使用 minWidth 和 minHeight 中的较大值）
});
```

### 固定矩形裁剪 (Fixed Rectangular)

裁剪区域的大小是固定的，但位置可以调整。

```js
editor.cropper.start({
  circular: false, // 矩形裁剪
  fixed: true, // 固定尺寸
  aspectRatio: 16/9 // 宽高比（例如 16:9）
});
```

### 固定圆形裁剪 (Fixed Circular)

裁剪区域是固定大小的圆形。

```js
editor.cropper.start({
  circular: true, // 圆形裁剪
  fixed: true // 固定尺寸
});
```

## 完整配置示例

以下是 Cropper 组件的完整配置示例：

```js
// 启动裁剪器并配置所有选项
editor.cropper.start({
  // 裁剪器类型
  circular: false, // 是否为圆形裁剪
  fixed: false, // 是否为固定尺寸裁剪
  
  // 尺寸约束
  keepRatio: true, // 是否保持宽高比
  aspectRatio: 1, // 宽高比（1 表示 1:1，即正方形）
  minWidth: 100, // 最小宽度（像素）
  minHeight: 100, // 最小高度（像素）
  marginRatio: 1.1, // 边界伸缩比例
  
  // 覆盖层样式
  overlay: {
    color: '#262626', // 覆盖层颜色
    opacity: 0.5 // 覆盖层透明度
  },
  
  // 裁剪区域变换器样式
  transformer: {
    borderStroke: '#ffffff', // 边框颜色
    borderStrokeWidth: 3, // 边框宽度
    anchorSize: 15, // 锚点大小
    anchorFill: '#ffffff', // 锚点填充颜色
    anchorStroke: '#ffffff', // 锚点边框颜色
    anchorStrokeWidth: 1, // 锚点边框宽度
    anchorCornerRadius: 30, // 锚点圆角半径
    borderDash: [0, 0] // 边框虚线样式 [实线长度, 间隙长度]
  },
  
  // 辅助线样式
  guides: {
    show: true, // 是否显示辅助线
    count: 3, // 每行/列的辅助线数量
    color: '#eeeeee', // 辅助线颜色
    width: 2, // 辅助线宽度
    dash: [15, 10, 15] // 辅助线虚线样式 [实线长度, 间隙长度, 实线长度, ...]
  }
});
```

## 使用裁剪结果

裁剪完成后，您可以使用以下方法获取裁剪结果：

```js
// 获取裁剪后的图像作为 Data URL
const imageDataURL = editor.cropper.getCroppedImage({
  mimeType: 'image/png', // 可选，输出格式（默认为 image/png）
  quality: 0.9, // 可选，输出质量（0-1，仅在 mimeType 为 image/jpeg 或 image/webp 时有效）
  pixelRatio: 1 // 可选，输出像素比
});

// 应用裁剪到当前画板（替换背景）
editor.cropper.apply();

// 停止裁剪器并恢复原始状态
editor.cropper.stop();
```

## 示例代码

以下是一个完整的裁剪示例，演示如何创建一个带有裁剪功能的图像编辑器：

```jsx
import { useState, useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function CropperExample() {
  const [ref, editor] = usePikaso();
  const [isCropping, setIsCropping] = useState(false);
  
  // 初始化时加载图像
  useEffect(() => {
    const initEditor = async () => {
      if (!editor) return;
      
      await editor.loadFromUrl(
        'https://images.unsplash.com/photo-1548199973-03cce0bbc87b?auto=format&fit=crop&w=1738&q=80'
      );
    };
    
    initEditor();
  }, [editor]);
  
  // 开始裁剪
  const startCropping = () => {
    if (!editor) return;
    
    // 定义裁剪器配置
    const cropperConfig = {
      circular: false, // 矩形裁剪
      fixed: false, // 灵活尺寸
      keepRatio: true, // 保持比例
      aspectRatio: 16/9, // 16:9 宽高比
      minWidth: 100,
      minHeight: 100,
      overlay: {
        color: '#262626',
        opacity: 0.5
      },
      guides: {
        show: true,
        count: 3,
        color: '#ffffff',
        width: 2,
        dash: [5, 5]
      }
    };
    
    // 启动裁剪器
    editor.cropper.start(cropperConfig);
    setIsCropping(true);
  };
  
  // 应用裁剪
  const applyCrop = () => {
    if (!editor) return;
    
    // 应用裁剪到画板
    editor.cropper.apply();
    
    // 停止裁剪状态
    editor.cropper.stop();
    setIsCropping(false);
  };
  
  // 取消裁剪
  const cancelCrop = () => {
    if (!editor) return;
    
    // 停止裁剪状态
    editor.cropper.stop();
    setIsCropping(false);
  };
  
  // 下载裁剪结果
  const downloadCroppedImage = () => {
    if (!editor) return;
    
    // 获取裁剪后的图像
    const croppedImageURL = editor.cropper.getCroppedImage({
      mimeType: 'image/jpeg',
      quality: 0.9
    });
    
    // 创建下载链接
    const a = document.createElement('a');
    a.href = croppedImageURL;
    a.download = 'cropped-image.jpg';
    a.click();
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          width: '600px',
          height: '400px'
        }}
      />
      
      <div style={{ marginTop: '20px', textAlign: 'center' }}>
        {!isCropping ? (
          <button onClick={startCropping}>
            开始裁剪
          </button>
        ) : (
          <div>
            <button onClick={applyCrop} style={{ marginRight: '10px' }}>
              应用裁剪
            </button>
            <button onClick={downloadCroppedImage} style={{ marginRight: '10px' }}>
              下载裁剪结果
            </button>
            <button onClick={cancelCrop}>
              取消裁剪
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
```

## 自定义裁剪器外观

裁剪器的外观是高度可定制的。以下是一些常见的自定义示例：

### 自定义覆盖层

```js
editor.cropper.start({
  // 其他配置...
  overlay: {
    color: '#000000', // 黑色覆盖层
    opacity: 0.7 // 较高的不透明度
  }
});
```

### 自定义边框和锚点

```js
editor.cropper.start({
  // 其他配置...
  transformer: {
    borderStroke: '#FF5733', // 橙红色边框
    borderStrokeWidth: 2, // 较细的边框
    anchorFill: '#33FFF5', // 青色锚点
    anchorSize: 10, // 较小的锚点
    anchorCornerRadius: 0, // 方形锚点
    borderDash: [10, 5] // 虚线边框
  }
});
```

### 自定义辅助线

```js
editor.cropper.start({
  // 其他配置...
  guides: {
    show: true, 
    count: 4, // 每行/列 4 条线（形成 5x5 的网格）
    color: '#FFFF33', // 黄色辅助线
    width: 1, // 较细的辅助线
    dash: [3, 3] // 点状虚线
  }
});
```

## API 参考

更多关于 Cropper 组件的详细信息，请参考 [Cropper API 文档](/api/classes/Cropper.html)。
