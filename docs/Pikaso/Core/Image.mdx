# Image

Image（图像）是另一个内置的 [Shape](/api/classes/ShapeModel.html)，它继承了 Shape 的所有方法和属性。

## 基本用法

```js
// 从 URL 创建新图像
editor.shapes.image.insert('<URL>', {
  // 配置
})

// 从文件创建新图像
editor.shapes.image.insert('<FILE>', {
  // 配置
})

// 从 Konva 对象创建新图像
const konvaImage = new Konva.Image({
  image: <Image>
})

editor.shapes.image.insert(konvaImage, {
  // 配置
})
```

## 图像示例

以下是一个完整的图像操作示例，展示如何添加和管理图像：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function ImageExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;
    
    // 初始化时添加一个随机图像
    addRandomImage();
  }, [editor]);

  // 添加随机图像
  const addRandomImage = () => {
    if (!editor) return;
    
    // 生成随机位置
    const x = Math.floor(Math.random() * (editor.board.stage.width() - 100));
    const y = Math.floor(Math.random() * (editor.board.stage.height() - 100));
    
    // 插入随机图像
    editor.shapes.image.insert(
      `https://source.unsplash.com/random/200x200?rand=${Math.random()}`,
      {
        x: x,
        y: y,
        drawBorder: true  // 绘制边框，使图像更容易被选择
      }
    );
  };

  // 从文件添加图像
  const handleFileUpload = (event) => {
    if (!editor || !event.target.files || !event.target.files[0]) return;
    
    const file = event.target.files[0];
    
    // 插入文件图像
    editor.shapes.image.insert(file, {
      x: 50,
      y: 50,
      draggable: true
    });
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '300px'
        }}
      />
      
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'space-between' }}>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
        />
        
        <button onClick={addRandomImage}>添加随机图片</button>
      </div>
    </div>
  );
}
```

## 图像属性

创建图像时，您可以设置各种属性来自定义其外观和行为：

```js
editor.shapes.image.insert('<URL>', {
  x: 100,                // X 坐标
  y: 100,                // Y 坐标
  width: 200,            // 宽度
  height: 200,           // 高度
  draggable: true,       // 是否可拖动
  opacity: 0.8,          // 透明度（0-1）
  rotation: 45,          // 旋转角度（度）
  scaleX: 1.2,           // X 轴缩放
  scaleY: 1.2,           // Y 轴缩放
  drawBorder: true,      // 是否绘制边框
  stroke: 'red',         // 边框颜色
  strokeWidth: 2,        // 边框宽度
  cornerRadius: 10,      // 圆角半径
  listening: true,       // 是否响应事件
  visible: true,         // 是否可见
  shadowBlur: 10,        // 阴影模糊度
  shadowColor: 'black',  // 阴影颜色
  shadowOffset: { x: 5, y: 5 }, // 阴影偏移
  shadowOpacity: 0.5,    // 阴影透明度
  name: 'myImage'        // 图像名称
});
```

## 图像cropWidth和cropHeight

Pikaso 允许您通过设置 `cropWidth` 和 `cropHeight` 属性来裁剪图像：

```js
editor.shapes.image.insert('<URL>', {
  x: 100,
  y: 100,
  cropWidth: 200,  // 裁剪宽度
  cropHeight: 150  // 裁剪高度
});
```

## 图像事件

您可以为图像添加事件监听器，响应用户交互：

```js
// 创建图像并保存引用
const image = editor.shapes.image.insert('<URL>', {
  x: 100,
  y: 100
});

// 监听点击事件
image.on('click', () => {
  console.log('图像被点击');
});

// 监听拖动事件
image.on('dragmove', () => {
  console.log('图像被拖动');
});

// 监听鼠标进入事件
image.on('mouseenter', () => {
  // 当鼠标进入时改变样式
  image.stroke('red');
  image.strokeWidth(3);
  editor.board.layer.batchDraw(); // 重新绘制以显示更改
});

// 监听鼠标离开事件
image.on('mouseleave', () => {
  // 恢复原始样式
  image.stroke('black');
  image.strokeWidth(1);
  editor.board.layer.batchDraw(); // 重新绘制以显示更改
});
```

## 图像操作

作为 ShapeModel 的子类，Image 继承了所有形状的操作方法：

```js
// 获取图像
const image = editor.shapes.image.insert('<URL>', { x: 100, y: 100 });

// 移动图像
image.x(200);
image.y(150);

// 缩放图像
image.scaleX(1.5);
image.scaleY(1.5);

// 旋转图像（角度）
image.rotation(45);

// 设置图像大小
image.width(300);
image.height(200);

// 更改图像透明度
image.opacity(0.8);

// 应用滤镜
image.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 5
  }
});

// 移除图像
image.remove();

// 恢复移除的图像
image.unremove();

// 克隆图像
const clonedImage = image.clone({
  x: image.x() + 50,
  y: image.y() + 50
});
```

## 从不同源加载图像

### 从 URL 加载

```js
// 从网络 URL 加载图像
editor.shapes.image.insert('https://example.com/image.jpg', {
  x: 100,
  y: 100
});

// 从数据 URL 加载图像
const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==';
editor.shapes.image.insert(dataUrl, {
  x: 100,
  y: 100
});
```

### 从文件对象加载

```js
// 从文件输入获取
const fileInput = document.querySelector('input[type="file"]');
fileInput.addEventListener('change', (event) => {
  const file = event.target.files[0];
  if (file) {
    editor.shapes.image.insert(file, {
      x: 100,
      y: 100
    });
  }
});

// 或者直接从拖放事件获取
document.addEventListener('drop', (event) => {
  event.preventDefault();
  
  if (event.dataTransfer.items) {
    for (let i = 0; i < event.dataTransfer.items.length; i++) {
      if (event.dataTransfer.items[i].kind === 'file') {
        const file = event.dataTransfer.items[i].getAsFile();
        editor.shapes.image.insert(file, {
          x: 100,
          y: 100
        });
        break;
      }
    }
  }
});
```

### 从 HTML 元素加载

```js
// 从现有 <img> 元素加载
const imgElement = document.getElementById('myImage');
const konvaImage = new Konva.Image({
  image: imgElement
});

editor.shapes.image.insert(konvaImage, {
  x: 100,
  y: 100
});

// 从 Canvas 元素加载
const canvas = document.getElementById('myCanvas');
const konvaImageFromCanvas = new Konva.Image({
  image: canvas
});

editor.shapes.image.insert(konvaImageFromCanvas, {
  x: 200,
  y: 200
});
