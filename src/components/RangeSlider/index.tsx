import { useState } from 'react'
import { Slider } from '@/components/ui/slider'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'

import { BasePopover } from '../BasePopover'

// 使用 shadcn-ui 的 Slider 组件，不需要额外样式

// 使用 shadcn-ui 的 Slider 组件，不需要自定义 Thumb 组件

interface Props {
  title: string
  defaultValue: number | number[]
  SliderProps?: React.ComponentPropsWithRef<typeof Slider>
  valueLabelFormat?: (value: number | number[]) => string | number | number[]
  onChange: (value: number | number[]) => void
}

export function RangeSlider({
  title,
  defaultValue,
  SliderProps,
  valueLabelFormat = value => value,
  onChange
}: Props) {
  const [value, setValue] = useState(defaultValue)

  const onChangeSlider = (newValue: number[]) => {
    const value = newValue[0]
    setValue(value)
    onChange(value)
  }

  return (
    <BasePopover
      buttonRenderer={({ id, isOpen }) => (
        <Button
          aria-describedby={id}
          variant="outline"
          className={`flex items-center gap-2 px-3 py-1 h-auto ${isOpen ? 'border-primary text-primary' : 'border-gray-300'}`}
        >
          <Avatar className="h-6 w-6 border border-gray-200 bg-primary text-primary-foreground">
            <AvatarFallback className="text-xs">
              {typeof valueLabelFormat === 'function'
                ? valueLabelFormat(value)
                : value ?? ''}
            </AvatarFallback>
          </Avatar>
          <span className="font-medium">{title}</span>
        </Button>
      )}
    >
      <div className="p-4 w-[300px]">
        <div className="py-4">
          <div className="text-center mb-2 text-sm font-medium">
            {typeof valueLabelFormat === 'function' ? valueLabelFormat(value) : value}
          </div>
          <Slider
            defaultValue={[typeof value === 'number' ? value : 0]}
            max={SliderProps?.max || 100}
            min={SliderProps?.min || 0}
            step={SliderProps?.step || 1}
            onValueChange={onChangeSlider}
            className="w-full"
          />
        </div>
      </div>
    </BasePopover>
  )
}
