import { Button } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useState } from "react"

interface ButtonProps {
  id: string | undefined
  isOpen: boolean
}

interface Props {
  buttonRenderer: (buttonProps: ButtonProps) => React.ReactNode
  children:
    | React.ReactNode
    | (({ close }: { close: () => void }) => React.ReactNode)
}

export function BasePopover({ buttonRenderer, children }: Props) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)

  const handleOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const open = Boolean(anchorEl)
  const id = open ? `base-popover` : undefined

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) handleClose()
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="p-0 h-auto bg-transparent hover:bg-transparent"
          onClick={handleOpen}
        >
          {buttonRenderer({
            id,
            isOpen: open,
          })}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto" align="start">
        {typeof children === "function"
          ? children({ close: handleClose })
          : children}
      </PopoverContent>
    </Popover>
  )
}
