import usePikaso from "@/hooks/usePikaso";
import type { Page, PageContent } from "@/types/album"; // 统一导入 Page 和 PageContent
import { resize } from "motion";
import { useEffect } from "react";
import { useTitle } from "react-use";

export default function PageThumbnail({ page }: { page?: Page | null }) {
  const pageContent: PageContent | undefined | null = page?.content;

  let aspectRatio = 1.0; // 默认宽高比 (方形)
  if (pageContent?.stage?.attrs?.width && pageContent.stage.attrs.width > 0 && pageContent.stage.attrs.height) {
    aspectRatio = pageContent.stage.attrs.height / pageContent.stage.attrs.width;
  } else if (page?.type === 'cover' || page?.type === 'back-cover') {
    // 封面的备用宽高比 (例如，更接近A4竖版)
    // aspectRatio = 297 / 210; // 示例，按需调整
  } else if (page?.type === 'content') {
    // 内容页的备用宽高比
    // aspectRatio = 4 / 3; // 示例，按需调整
  }

  useTitle("Pikaso | Core Components | Import/Export"); // 如果项目需要，保留此行

  const [ref, editor] = usePikaso({
    selection: {
      interactive: false, // 禁用交互式选择
      transformer: {
        borderStroke: "#262626",
        anchorFill: "#262626",
      },
    },
  });

  useEffect(() => {
    // 处理将数据加载到编辑器
    if (editor) {
      if (pageContent && Object.keys(pageContent).length > 0) {
        editor.load(JSON.stringify(pageContent));
      } else {
        // 如果没有页面数据，加载一个默认的空白状态
        // 画布尺寸应符合当前的aspectRatio
        const defaultStageWidth = 500;
        const defaultStageHeight = defaultStageWidth * aspectRatio;
        editor.load(JSON.stringify({
          stage: { attrs: { width: defaultStageWidth, height: defaultStageHeight, x: 0, y: 0 }, filters: [], className: 'Stage' },
          layer: { attrs: { x: 0, y: 0, width: defaultStageWidth, height: defaultStageHeight }, filters: [], className: 'Layer' },
          background: { image: {}, overlay: {} }, // 假设这些是Pikaso数据结构的一部分
          shapes: []
        }));
      }
    }
  }, [editor, pageContent, aspectRatio]);

  useEffect(() => {
    // 处理画布容器和Pikaso板的尺寸调整
    if (!editor || !ref.current) {
      return;
    }

    const containerElement = ref.current;

    const setSizeAndRescale = (currentWidth: number) => {
      if (currentWidth <= 0) {
        // 如果宽度无效，则跳过调整大小
        containerElement.style.height = '0px'; // 或者设置为一个合理的最小高度
        return;
      }
      const newHeight = currentWidth * aspectRatio;
      containerElement.style.height = `${newHeight}px`;
      // Pikaso的board.rescale()应在其容器div具有新尺寸后调用
      editor.board.rescale();
    };

    // 使用 motion 的 resize 观察器
    // 回调接收 (element, info: DOMRectReadOnly)
    const stopObserving = resize(containerElement, (_, info) => {
      setSizeAndRescale(info.width);
    });

    // 初始尺寸设置：
    // 组件已渲染，containerElement 应具有由CSS确定的初始宽度
    const initialWidth = containerElement.offsetWidth;
    if (initialWidth > 0) {
      setSizeAndRescale(initialWidth);
    }

    return () => {
      // 清理观察器
      stopObserving();
    };
  }, [editor, ref, aspectRatio]); // ref 是稳定的。editor 和 aspectRatio 是关键依赖项。

  return <div ref={ref} className="canvas-container" />;
}
