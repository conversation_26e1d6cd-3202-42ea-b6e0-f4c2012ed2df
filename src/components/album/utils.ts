import type { Page } from "@/types/album"

export function calculateDisplayPageNumber(
  page: Page | null,
  spreadIndex: number,
  isLeftPage: boolean,
): string | null {
  // 空白页不显示页码
  if (!page) return null

  // 封面和封底保持原有显示
  if (page.type === "cover") return "封面"
  if (page.type === "back-cover") return "封底"

  // 内容页的页码计算
  if (page.type === "content") {
    // 第一个扩展页（spreadIndex = 0）
    if (spreadIndex === 0) {
      // 左页是空白页，不显示页码
      if (isLeftPage) return null
      // 右页显示"1"
      return "1"
    }

    // 后续扩展页的页码计算
    // spreadIndex = 1: 左页显示"2"，右页显示"3"
    // spreadIndex = 2: 左页显示"4"，右页显示"5"
    // 以此类推...
    const basePageNumber = spreadIndex * 2
    return isLeftPage
      ? basePageNumber.toString()
      : (basePageNumber + 1).toString()
  }

  return null
}