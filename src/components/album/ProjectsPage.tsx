import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { useQuery } from "@tanstack/react-query"
import { ArrowRight, Edit, Loader2, Menu, MoreHorizontal } from "lucide-react"
import type React from "react"
import { getAlbums } from "../../services/albumService"
import type { Album } from "../../types/album"
import "./albumStyles.css"
import { useNavigate } from "@tanstack/react-router"

const AlbumsPage: React.FC = () => {
  // 定义标签映射，保持英文key但显示中文
  const tabLabels = {
    inProgress: "进行中",
    ordered: "已下单",
    archived: "已归档",
  }

  const {
    data: albums,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["albums"],
    queryFn: getAlbums,
  })

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="flex items-center space-x-4">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">加载中...</span>
        </div>
      </div>
    )
  }

  if (error || !albums) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <p className="text-lg text-red-500">加载相册失败，请稍后再试</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 bg-white">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Projects</h1>
      </div>

      <Tabs defaultValue="inProgress" className="mb-6">
        <TabsList className="bg-gray-100 rounded-full p-1">
          <TabsTrigger
            value="inProgress"
            className="rounded-full py-2 px-6 text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            {tabLabels.inProgress}
          </TabsTrigger>
          <TabsTrigger
            value="ordered"
            className="rounded-full py-2 px-6 text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            {tabLabels.ordered}
          </TabsTrigger>
          <TabsTrigger
            value="archived"
            className="rounded-full py-2 px-6 text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            {tabLabels.archived}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="inProgress" className="mt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {albums
              .filter((album) => album.status === "inProgress" || !album.status) // 默认展示或筛选
              .map((album) => (
                <ProjectCard key={album.id} album={album} />
              ))}
          </div>
        </TabsContent>

        <TabsContent value="ordered" className="mt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {albums
              .filter((album) => album.status === "ordered")
              .map((album) => (
                <ProjectCard key={album.id} album={album} />
              ))}
          </div>
        </TabsContent>

        <TabsContent value="archived" className="mt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {albums
              .filter((album) => album.status === "archived")
              .map((album) => (
                <ProjectCard key={album.id} album={album} />
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface ProjectCardProps {
  album: Album
}

const ProjectCard: React.FC<ProjectCardProps> = ({ album }) => {
  const navigate = useNavigate()

  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <div className="relative mb-4 w-full aspect-[4/3] mx-site ">
        <div className="components-productThumbnail">
          <div
            className="components-productThumbnail-container components-productThumbnail-hero
          
          w-full h-full 
          flex
           items-center justify-center"
          >
            <div
              className="components-productThumbnail-contour 
              components-productThumbnail-photobook
               components-productThumbnail-square 
                components-productThumbnail-skeuomorphic-book"
              style={{ width: "100%", height: "100%" }}
            >
              <div className="components-productThumbnail-spine">
                <img
                  src={album.coverImage}
                  alt={album.title}
                  title={album.title}
                />
              </div>
              <div className="components-productThumbnail-book">
                <img
                  src={album.coverImage}
                  alt={album.title}
                  title={album.title}
                />
              </div>

              <div className="controls-block-width-fit controls-block-height-fit components-productThumbnail-mask" />
            </div>
          </div>
        </div>
      </div>
      <div className="text-center mb-4">
        <h3 className="text-xl font-bold">{album.title}</h3>
        <p className="text-sm text-gray-500">Last Edited: 1 minute ago</p>
      </div>
      <div className="flex justify-center gap-2 items-center">
        <button
          type="button"
          className="flex items-center gap-2 py-2 px-4  text-white rounded-full bg-indigo-500"
          onClick={() => navigate({ to: "/album" })}
        >
          <Edit size={20} />
          <span>编辑</span>
        </button>

        <button
          type="button"
          className="flex items-center gap-2 py-2 px-4 bg-indigo-500 text-white rounded-full"
          onClick={() => navigate({ to: "/preview" })}
        >
          <span>预览</span>
          <ArrowRight size={16} />
        </button>
      </div>
    </div>
  )
}

export default AlbumsPage
