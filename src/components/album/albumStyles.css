.mx-site .components-productThumbnail {
  display: block;
  max-width: 18rem;
  position: relative;
  width: 100%;
}


.mx-site .components-productThumbnail:before {
  content: "";
  display: block;
  padding-bottom: 100%;
}

.mx-site .components-productThumbnail-container.components-productThumbnail-hero {
  padding: 16px;
}

.mx-site .components-productThumbnail-container {
  left: 0;
  position: absolute;
  top: 0;
}

.mx-site .components-productThumbnail-contour {
  max-height: 100%;
  max-width: 100%;
  overflow: visible;
  position: relative;
}

.mx-site * {
  background: transparent;
  border: 0;
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding: 0;
}

.mx-site .components-productThumbnail-contour:before {
  background: linear-gradient(45deg, rgb(0 0 0 / 5%), transparent);
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}

.mx-site .components-productThumbnail-spine {
  border-radius: 1px 2px 2px 1px;
  height: 100%;
  overflow: hidden;
  position: absolute;
  width: 6%;
}

.mx-site .components-productThumbnail-spine img {
  height: 100%;
  max-width: inherit;
  position: absolute;
}

.mx-site .components-productThumbnail-spine:after {
  background-position: 0 0;
  border-radius: 1px 2px 2px 1px;
  box-shadow: inset -1px 1px 1px hsla(0, 0%, 100%, .12), inset 0 0 2px rgba(51, 51, 51, .12), inset 0 0 3px rgba(51, 51, 51, .18);
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
}

.mx-site .components-productThumbnail-book {
  background-position: 100% 0;
  background-size: 200px 200px;
  border-radius: 2px 3px 3px 2px;
  bottom: 0;
  box-shadow: inset -1px -1px 1px rgba(0, 0, 0, .2), inset 2px 1px 1px hsla(0, 0%, 100%, .2), inset 0 0 2px rgba(51, 51, 51, .3);
  left: 5.8%;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  width: 94.2%;
}

.mx-site .components-productThumbnail-book img {
  height: 100%;
  left: -6%;
  position: absolute;
  width: 106%;
  max-width: inherit;
}

.mx-site .components-productThumbnail-book:after {
  background-position: 100% 0;
  background-size: 200px 200px;
  border-radius: 2px 3px 3px 2px;
  box-shadow: inset -1px -1px 1px rgba(0, 0, 0, .2), inset 2px 1px 1px hsla(0, 0%, 100%, .2), inset 0 0 2px rgba(51, 51, 51, .3);
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
}

.mx-site .components-productThumbnail-mask {
  background-size: 100% 100%;
  left: 0;
  position: absolute;
  top: 0;
  z-index: 1;
}


.mx-site .components-productThumbnail-skeuomorphic-book:before {
  bottom: 3px;
  box-shadow: 0 0 2px rgba(0, 0, 0, .1), 0 10px 20px rgba(51, 51, 51, .15), 0 12px 15px rgba(0, 0, 0, .3), 0 6px 6px rgba(0, 0, 0, .2);
  content: "";
  display: block;
  left: 0;
  position: absolute;
  right: 1px;
  top: 4px;
  z-index: 0;
}

.mx-site .components-productThumbnail-skeuomorphic-book:after {
  background: linear-gradient(90deg, rgba(51, 51, 51, 0), rgba(51, 51, 51, .25) 48.96%, rgba(51, 51, 51, 0));
  bottom: 1px;
  content: "";
  display: block;
  left: 4%;
  opacity: .2;
  position: absolute;
  top: 1px;
  width: 6%;
}

.mx-site .components-productThumbnail-skeuomorphic-book.components-productThumbnail-contour {
  overflow: inherit;
}
