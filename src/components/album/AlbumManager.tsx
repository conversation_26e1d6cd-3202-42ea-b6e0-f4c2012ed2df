import { But<PERSON> } from "@/components/ui/button"
import {
  useAlbumStore,
  useAlbumStoreCanUndoRedo,
  useAlbumStoreUndo,
} from "@/stores/albumStore"
import { useEditorStore } from "@/stores/editorStore"
import type { Page } from "@/types/album"
import {
  DndContext,
  type DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import {
  SortableContext,
  horizontalListSortingStrategy,
  sortableKeyboardCoordinates,
  useSortable,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { useNavigate } from "@tanstack/react-router"
import {
  ArrowLeft,
  Clock,
  Download,
  Edit3,
  Eye,
  HelpCircle,
  RotateCcw,
  RotateCw,
  Save,
  Share2,
  Upload,
} from "lucide-react"
import type { ReactElement } from "react"
import { useEffect, useRef, useState } from "react"
import { BackCoverPageBlock } from "./BackCoverPageBlock"
import { CoverPageBlock } from "./CoverPageBlock"
import { DeleteConfirmDialog } from "./DeleteConfirmDialog"
import { SpreadPageBlock } from "./SpreadPageBlock"

export function AlbumManager() {
  const navigate = useNavigate()
  const {
    album,
    createAlbum,
    updateAlbumName,
    insertPageAfter,
    deletePage,
    setCurrentPage,
    reorderPages,
    exportAlbum,
    importAlbum,
  } = useAlbumStore()

  // 获取 editorStore 的设置方法来传递页面数据
  const { setEditorData } = useEditorStore()

  const { undo, redo } = useAlbumStoreUndo()
  const { canUndo, canRedo } = useAlbumStoreCanUndoRedo()
  const [deletePageId, setDeletePageId] = useState<string | null>(null)
  const [isSaved, setIsSaved] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [isEditingAlbumName, setIsEditingAlbumName] = useState(false)
  const [albumNameInput, setAlbumNameInput] = useState("")

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  useEffect(() => {
    if (!album) {
      createAlbum("我的相册")
    }
  }, [album, createAlbum])

  useEffect(() => {
    setIsSaved(true)
  }, [])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case "z":
            event.preventDefault()
            if (event.shiftKey) {
              redo()
            } else {
              undo()
            }
            break
          case "y":
            event.preventDefault()
            redo()
            break
          case "s":
            event.preventDefault()
            setIsSaved(true)
            break
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [undo, redo])

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setIsDragging(false)

    if (over && active.id !== over.id) {
      const oldIndex = album?.pages.findIndex((p) => p.id === active.id) ?? 0
      const newIndex = album?.pages.findIndex((p) => p.id === over.id) ?? 0
      reorderPages(oldIndex, newIndex)
    }
  }

  const handleInsertPageAfter = (pageId: string) => {
    insertPageAfter(pageId)
  }

  const handleDeletePage = (pageId: string) => {
    setDeletePageId(pageId)
  }

  const confirmDeletePage = () => {
    if (deletePageId) {
      deletePage(deletePageId)
      setDeletePageId(null)
    }
  }

  const handleEditPage = (pageId: string) => {
    // 设置 albumStore 的当前页面
    setCurrentPage(pageId)

    // 传递页面数据并设置当前页面ID给 editorStore
    if (album?.pages) {
      setEditorData(album.pages, pageId)
    }

    navigate({ to: "/editor" })
  }

  const handlePreview = () => {
    navigate({ to: "/preview" })
  }

  const handleShare = () => {
    if (navigator.share && album) {
      navigator
        .share({
          title: album.name,
          text: `查看我的相册: ${album.name}`,
          url: window.location.href,
        })
        .catch(console.error)
    } else {
      navigator.clipboard
        .writeText(window.location.href)
        .then(() => alert("链接已复制到剪贴板"))
        .catch(() => alert("分享功能暂不可用"))
    }
  }

  const handleOrder = () => {
    console.log("Order album", album)
  }

  const handleExport = () => {
    const data = exportAlbum()
    if (data) {
      const blob = new Blob([data], { type: "application/json" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `${album?.name || "album"}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  const handleImport = () => {
    const input = document.createElement("input")
    input.type = "file"
    input.accept = ".json"
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const data = e.target?.result as string
          if (importAlbum(data)) {
            alert("相册导入成功")
          } else {
            alert("相册导入失败，请检查文件格式")
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  const handleEditAlbumName = () => {
    if (album) {
      setAlbumNameInput(album.name)
      setIsEditingAlbumName(true)
    }
  }

  // 保存相册名称
  const handleSaveAlbumName = () => {
    if (album && albumNameInput.trim()) {
      updateAlbumName(albumNameInput.trim())
      setIsEditingAlbumName(false)
    }
  }

  // 取消编辑相册名称
  const handleCancelEditAlbumName = () => {
    setIsEditingAlbumName(false)
    setAlbumNameInput("")
  }

  // 处理输入框回车和ESC键
  const handleAlbumNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveAlbumName()
    } else if (e.key === "Escape") {
      handleCancelEditAlbumName()
    }
  }

  // 渲染页面块的函数 - 实现新的展示逻辑
  const renderPageBlocks = () => {
    // 安全检查：确保 album 存在且 pages 是数组
    if (!album || !Array.isArray(album.pages) || album.pages.length === 0)
      return null

    const pages = album.pages
    const blocks: ReactElement[] = []

    // 1. 封面页面块（第一页，type === 'cover'）
    const coverPage = pages.find((page) => page.type === "cover")
    if (coverPage) {
      blocks.push(
        <CoverPageBlock
          key={coverPage.id}
          page={coverPage}
          onEdit={handleEditPage}
          onDelete={handleDeletePage}
          isDragging={isDragging}
        />,
      )
    }

    // 2. 内容页按扩展页显示（左右两页并排）
    const contentPages = pages
      .filter((page) => page.type === "content")
      .sort((a, b) => a.index - b.index)

    // 将内容页按索引成对分组，第一个扩展页的左页为空白（封面背面）
    for (let i = 0; i < contentPages.length; i += 2) {
      const leftPage = i === 0 ? null : contentPages[i] // 第一个扩展页左页为空白
      const rightPage = i === 0 ? contentPages[i] : contentPages[i + 1]

      if (rightPage) {
        blocks.push(
          <SpreadPageBlock
            key={`spread-${leftPage?.id || "empty"}-${rightPage.id}`}
            leftPage={leftPage}
            rightPage={rightPage}
            spreadIndex={Math.floor(i / 2)}
            onEdit={handleEditPage}
            onDelete={handleDeletePage}
            onInsertAfter={handleInsertPageAfter}
            isDragging={isDragging}
          />,
        )
      }
    }

    // 3. 封底页面块（最后一页，type === 'back-cover'）
    const backCoverPage = pages.find((page) => page.type === "back-cover")
    if (backCoverPage) {
      blocks.push(
        <BackCoverPageBlock
          key={backCoverPage.id}
          page={backCoverPage}
          onEdit={handleEditPage}
          onDelete={handleDeletePage}
          isDragging={isDragging}
        />,
      )
    }

    return (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragStart={() => setIsDragging(true)}
      >
        <SortableContext
          items={pages.map((page) => page.id)}
          strategy={horizontalListSortingStrategy}
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
            {blocks}
          </div>
        </SortableContext>
      </DndContext>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 overflow-x-hidden">
      <div className="bg-gray-800 text-white px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="flex items-center justify-between md:justify-start md:space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate({ to: "/" })}
                className="flex items-center space-x-2 text-white hover:bg-gray-700"
              >
                <ArrowLeft size={16} />
                <span>返回</span>
              </Button>
              <div className="flex items-center space-x-2 ml-4 md:ml-0">
                {isEditingAlbumName ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={albumNameInput}
                      onChange={(e) => setAlbumNameInput(e.target.value)}
                      onKeyDown={handleAlbumNameKeyDown}
                      onBlur={handleSaveAlbumName}
                      className="bg-gray-700 text-white px-2 py-1 rounded border border-gray-600 focus:border-purple-500 focus:outline-none text-lg font-semibold min-w-[200px]"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSaveAlbumName}
                      className="text-green-400 hover:bg-gray-700 p-1"
                    >
                      <Save size={16} />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-semibold truncate">
                      {album?.name || "我的相册"}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleEditAlbumName}
                      className="text-gray-300 hover:bg-gray-700 hover:text-white p-1"
                      title="编辑相册名称"
                    >
                      <Edit3 size={16} />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-center space-x-2 md:space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="bg-purple-600 text-white border-purple-600 hover:bg-purple-700 flex-1 max-w-[100px] md:flex-none md:max-w-none"
              >
                <Share2 size={16} className="mr-1" />
                <span className="">分享</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleImport}
                className="flex items-center space-x-1 text-gray-600 text-xs md:text-sm px-2 md:px-3"
              >
                <Upload size={14} className="md:w-4 md:h-4" />
                <span>导入</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="bg-gray-700 text-white border-gray-600 hover:bg-gray-600 flex-1 max-w-[100px] md:flex-none md:max-w-none"
              >
                <Download size={16} className="mr-1" />
                <span className="">导出</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleOrder}
                className="bg-white text-gray-800 hover:bg-gray-100 flex-1 max-w-[100px] md:flex-none md:max-w-none"
              >
                <span className="">订购</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="flex flex-col space-y-3 md:flex-row md:items-center md:space-y-0 md:space-x-4">
              <div className="flex items-center justify-between md:justify-start md:space-x-4">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Clock size={16} />
                  <span className="text-sm">
                    {isSaved ? "已保存" : "保存中..."}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => undo()}
                    disabled={!canUndo}
                    className="p-2"
                    title="撤销 (Ctrl+Z)"
                  >
                    <RotateCcw size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => redo()}
                    disabled={!canRedo}
                    className="p-2"
                    title="重做 (Ctrl+Y)"
                  >
                    <RotateCw size={16} />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between space-x-2 md:space-x-3">
              <div className="flex items-center space-x-2 md:space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePreview}
                  className="flex items-center space-x-1 text-purple-600 text-xs md:text-sm px-2 md:px-3"
                >
                  <Eye size={14} className="md:w-4 md:h-4" />
                  <span>预览</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-2 py-4 md:px-4 md:py-6">
        <div className="space-y-6">{renderPageBlocks()}</div>
      </div>

      <div className="fixed bottom-4 right-4 md:bottom-6 md:right-6">
        <Button
          variant="outline"
          size="sm"
          className="bg-white shadow-lg border-gray-300 text-purple-600 hover:bg-purple-50"
        >
          <HelpCircle size={16} className="mr-2" />
          Help
        </Button>
      </div>

      {isDragging && (
        <div className="fixed top-0 left-0 right-0 bg-purple-600 text-white py-2 text-center z-50">
          现在可以拖拽重新排序页面
        </div>
      )}

      <DeleteConfirmDialog
        open={deletePageId !== null}
        onOpenChange={(open) => !open && setDeletePageId(null)}
        onConfirm={confirmDeletePage}
        spreadName={
          deletePageId && album
            ? (() => {
                const page = album.pages.find((p) => p.id === deletePageId)
                if (!page) return ""
                if (page.type === "cover") return "封面"
                if (page.type === "back-cover") return "封底"
                return `第 ${page.index} 页`
              })()
            : ""
        }
      />
    </div>
  )
}
