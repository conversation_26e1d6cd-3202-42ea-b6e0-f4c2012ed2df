import { But<PERSON> } from "@/components/ui/button"
import type { Page } from "@/types/album"
import { Edit3 } from "lucide-react"
import PageThumbnail from "./PageThumbnail"

interface CoverPageBlockProps {
  page: Page
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  isDragging: boolean
}

export function CoverPageBlock({
  page,
  onEdit,
  onDelete,
  isDragging,
}: CoverPageBlockProps) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
      <div className="relative">
        <button
          type="button"
          className="w-full bg-gray-50 p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
          onClick={() => onEdit(page.id)}
        >
          <div className="grid grid-cols-1 gap-1.5">
            <div className="relative aspect-square bg-white rounded border border-gray-200 overflow-hidden">
              <PageThumbnail page={page} />
              <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                封面
              </div>
            </div>
          </div>
        </button>
      </div>

      <div className="p-4 bg-gray-50/50">
        <div className="text-center mb-3">
          <span className="text-lg font-semibold text-gray-800">封面</span>
        </div>

        <div className="flex justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(page.id)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title="编辑封面"
          >
            <Edit3 size={16} />
          </Button>
        </div>
      </div>
    </div>
  )
}