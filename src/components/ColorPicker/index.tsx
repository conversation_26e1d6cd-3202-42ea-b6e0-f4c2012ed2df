import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { SketchPicker } from 'react-color'
import type { ColorResult } from 'react-color'

import { BasePopover } from '../BasePopover'

interface Props {
  title: string
  defaultColor: string
  onChange: (color: string) => void
}

export function ColorPicker({ title, defaultColor, onChange }: Props) {
  const [color, setColor] = useState(defaultColor)

  const onSwatchHover = (color: ColorResult) => {
    setColor(color.hex)
    onChange(color.hex)
  }

  const onChangeColor = (color: ColorResult) => {
    setColor(color.hex)
    onChange(color.hex)
  }

  return (
    <BasePopover
      buttonRenderer={({ id, isOpen }) => (
        <Button
          aria-describedby={id}
          variant="outline"
          className={`flex items-center gap-2 px-3 py-1 h-auto ${isOpen ? 'border-primary text-primary' : 'border-gray-300'}`}
        >
          <Avatar className="h-6 w-6 border border-gray-200" style={{ backgroundColor: color }}>
            <AvatarFallback className="bg-transparent" />
          </Avatar>
          <span className="font-medium">{title}</span>
        </Button>
      )}
    >
      <SketchPicker
        color={color}
        presetColors={[]}
        onSwatchHover={onSwatchHover}
        onChange={onChangeColor}
      />
    </BasePopover>
  )
}
