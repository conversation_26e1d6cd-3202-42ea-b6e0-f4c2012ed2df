import type Konva from "konva"
import type { ImageCropPosition } from "../shape/drawers/ImageDrawer"

/**
 * 计算图片裁剪参数，保持比例不变
 *
 * @param image 图片元素
 * @param size 目标尺寸
 * @param clipPosition 对齐方式
 * @returns 裁剪参数
 */
export function getCrop(
  image: HTMLImageElement,
  size: { width: number; height: number },
  clipPosition: ImageCropPosition = "center-middle",
) {
  const width = size.width
  const height = size.height
  const aspectRatio = width / height

  let newWidth: number
  let newHeight: number

  const imageRatio = image.width / image.height

  if (aspectRatio >= imageRatio) {
    newWidth = image.width
    newHeight = image.width / aspectRatio
  } else {
    newWidth = image.height * aspectRatio
    newHeight = image.height
  }

  let x = 0
  let y = 0

  if (clipPosition === "left-top") {
    x = 0
    y = 0
  } else if (clipPosition === "left-middle") {
    x = 0
    y = (image.height - newHeight) / 2
  } else if (clipPosition === "left-bottom") {
    x = 0
    y = image.height - newHeight
  } else if (clipPosition === "center-top") {
    x = (image.width - newWidth) / 2
    y = 0
  } else if (clipPosition === "center-middle") {
    x = (image.width - newWidth) / 2
    y = (image.height - newHeight) / 2
  } else if (clipPosition === "center-bottom") {
    x = (image.width - newWidth) / 2
    y = image.height - newHeight
  } else if (clipPosition === "right-top") {
    x = image.width - newWidth
    y = 0
  } else if (clipPosition === "right-middle") {
    x = image.width - newWidth
    y = (image.height - newHeight) / 2
  } else if (clipPosition === "right-bottom") {
    x = image.width - newWidth
    y = image.height - newHeight
  }

  return {
    cropX: x,
    cropY: y,
    cropWidth: newWidth,
    cropHeight: newHeight,
  }
}

/**
 * 应用裁剪到 Konva 图片实例
 *
 * @param img Konva 图片实例
 * @param position 对齐方式
 */
export function applyCrop(img: Konva.Image, position: ImageCropPosition) {
  img.setAttr("lastCropUsed", position)
  const imageElement = img.image() as HTMLImageElement

  if (!imageElement) return

  const crop = getCrop(
    imageElement,
    { width: img.width(), height: img.height() },
    position,
  )
  img.setAttrs(crop)
}

/**
 * 获取所有可用的对齐方式
 *
 * @returns 对齐方式数组
 */
export function getAllCropPositions(): ImageCropPosition[] {
  return [
    "left-top",
    "center-top",
    "right-top",
    "left-middle",
    "center-middle",
    "right-middle",
    "left-bottom",
    "center-bottom",
    "right-bottom",
  ]
}

/**
 * 验证对齐方式是否有效
 *
 * @param position 要验证的对齐方式
 * @returns 是否有效
 */
export function isValidCropPosition(
  position: string,
): position is ImageCropPosition {
  return getAllCropPositions().includes(position as ImageCropPosition)
}

/**
 * 获取对齐方式的描述
 *
 * @param position 对齐方式
 * @returns 描述文本
 */
export function getCropPositionDescription(
  position: ImageCropPosition,
): string {
  const descriptions: Record<ImageCropPosition, string> = {
    "left-top": "左上角对齐",
    "center-top": "顶部居中对齐",
    "right-top": "右上角对齐",
    "left-middle": "左侧居中对齐",
    "center-middle": "完全居中对齐",
    "right-middle": "右侧居中对齐",
    "left-bottom": "左下角对齐",
    "center-bottom": "底部居中对齐",
    "right-bottom": "右下角对齐",
  }
  return descriptions[position] || "未知对齐方式"
}

/**
 * 获取对齐方式的网格位置（用于UI布局）
 *
 * @param position 对齐方式
 * @returns 网格位置 {row, col}
 */
export function getCropPositionGridLocation(position: ImageCropPosition): {
  row: number
  col: number
} {
  const positions = getAllCropPositions()
  const index = positions.indexOf(position)
  return {
    row: Math.floor(index / 3),
    col: index % 3,
  }
}

// 为了向后兼容，也导出一个包含所有函数的对象
export const ImageCropUtils = {
  getCrop,
  applyCrop,
  getAllCropPositions,
  isValidCropPosition,
  getCropPositionDescription,
  getCropPositionGridLocation,
}
