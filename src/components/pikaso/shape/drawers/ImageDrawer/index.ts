import Konva from "konva"

import { createImageFromUrl } from "../../../utils/create-image-from-url"
import { isBrowser } from "../../../utils/detect-environment"
import { applyCrop } from "../../../utils/image-crop-utils"
import { imageToDataUrl } from "../../../utils/image-to-url"

import type { Board } from "../../../Board"
import { ImageModel } from "../../models/ImageModel"

// 图片对齐方式类型定义
export type ImageCropPosition =
  | "left-top"
  | "center-top"
  | "right-top"
  | "left-middle"
  | "center-middle"
  | "right-middle"
  | "left-bottom"
  | "center-bottom"
  | "right-bottom"

// 扩展 ImageConfig 接口以支持对齐方式
export interface ExtendedImageConfig extends Partial<Konva.ImageConfig> {
  cropPosition?: ImageCropPosition
}

export class ImageDrawer {
  /**
   * Represents the [[Board]]
   */
  private board: Board

  /**
   * Creates a new image builder component
   *
   * @param board The [[Board]
   */
  constructor(board: Board) {
    this.board = board
  }

  /**
   * Inserts a new image into the board
   *
   * @param image The image [[File]]
   * @param config The image node configuration
   */
  public async insert(
    image: File | Konva.Image | string,
    config: ExtendedImageConfig = {},
  ): Promise<ImageModel> {
    let imageInstance: Konva.Image

    if (image instanceof Konva.Image) {
      imageInstance = image
    } else {
      const url =
        isBrowser() && image instanceof File
          ? await imageToDataUrl(image)
          : image

      imageInstance = await createImageFromUrl(url as string)
    }

    const ratio = imageInstance.width() / imageInstance.height()
    const defaultHeight = this.board.stage.height() / 2
    const defaultWidth = defaultHeight * ratio

    // 提取对齐方式配置
    const { cropPosition = "center-middle", ...konvaConfig } = config

    imageInstance.setAttrs({
      width: defaultWidth,
      height: defaultHeight,
      x: (this.board.stage.width() - defaultWidth) / 2,
      y: (this.board.stage.height() - defaultHeight) / 2,
      rotation: this.board.stage.rotation() * -1,
      draggable: this.board.settings.selection?.interactive,
      ...konvaConfig,
    })

    // 应用初始裁剪
    applyCrop(imageInstance, cropPosition)

    const imageModel = new ImageModel(this.board, imageInstance, {
      transformer: {
        keepRatio: false,
        flipEnabled: false,
        boundBoxFunc: (oldBox, newBox) => {
          if (Math.abs(newBox.width) < 10 || Math.abs(newBox.height) < 10) {
            return oldBox
          }
          return newBox
        },
        // enabledAnchors: [
        //   "top-left",
        //   "top-right",
        //   "bottom-left",
        //   "bottom-right",
        // ],
      },
    })

    // 设置对齐方式到模型中
    imageModel.setCropPosition(cropPosition)

    return imageModel
  }
}
