import Konva from 'konva'

import { rotateAroundCenter } from '../../../utils/rotate-around-center'
import { ShapeModel } from '../../ShapeModel'

export class TextModel extends ShapeModel<Konva.Text, Konva.TextConfig> {
  /**
   * @inheritdoc
   */
  public get type(): string {
    return 'text'
  }

  /**
   * @inheritdoc
   * @override
   */
  public rotate(theta: number) {
    rotateAroundCenter(this.node, theta)

    this.board.events.emit('shape:rotate', {
      shapes: [this]
    })
  }
  /**
   * Sets padding of the text node.
   *
   * @param padding The new padding value in pixels.
   */
  public setPadding(padding: number) {
    this.update({ padding })
  }

  /**
   * Increases or decreases padding of the text node.
   *
   * @param delta The change in padding (positive to increase, negative to decrease).
   */
  public increasePadding(delta: number) {
    const current = this.node.padding() || 0
    this.update({ padding: Math.max(0, current + delta) })
  }
}
