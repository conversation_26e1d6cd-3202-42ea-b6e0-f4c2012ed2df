import type Konva from "konva"

import type { Board } from "../../../Board"
import { applyCrop } from "../../../utils/image-crop-utils"
import { rotateAroundCenter } from "../../../utils/rotate-around-center"

import type { ShapeConfig } from "../../../types"
import { ShapeModel } from "../../ShapeModel"
import type { ImageCropPosition } from "../../drawers/ImageDrawer"

export class ImageModel extends ShapeModel<Konva.Image, Konva.ImageConfig> {
  /**
   * 当前图片的对齐方式
   */
  private cropPosition: ImageCropPosition = "center-middle"

  /**
   * 创建一个新的图像模型
   *
   * @param board 画板实例
   * @param node 图像节点
   * @param config 配置选项
   */
  constructor(board: Board, node: Konva.Image, config: ShapeConfig = {}) {
    super(board, node, config)
    // 绑定变换事件，保持裁剪比例
    node.on("transform", this.onTransform.bind(this))
  }

  /**
   * @inheritdoc
   */
  public get type(): string {
    return "image"
  }

  /**
   * @inheritdoc
   * @override
   */
  public rotate(theta: number) {
    rotateAroundCenter(this.node, theta)

    this.board.events.emit("shape:rotate", {
      shapes: [this],
    })
  }

  /**
   * 设置图片的对齐方式
   *
   * @param position 对齐方式
   */
  public setCropPosition(position: ImageCropPosition) {
    this.cropPosition = position
    this.node.setAttr("lastCropUsed", position)
  }

  /**
   * 获取当前的对齐方式
   *
   * @returns 当前对齐方式
   */
  public getCropPosition(): ImageCropPosition {
    return this.cropPosition
  }

  /**
   * 变换事件处理，保持裁剪比例
   */
  private onTransform() {
    // 重置缩放，保持宽高比例
    this.node.setAttrs({
      scaleX: 1,
      scaleY: 1,
      width: this.node.width() * this.node.scaleX(),
      height: this.node.height() * this.node.scaleY(),
    })

    // 重新应用裁剪
    this.applyCropToSelf()
  }

  /**
   * 应用裁剪到自身
   */
  public applyCropToSelf() {
    applyCrop(this.node, this.cropPosition)
  }
}
