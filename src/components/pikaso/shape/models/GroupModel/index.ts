import Konva from 'konva'

import { rotateAroundCenter } from '../../../utils/rotate-around-center'

import { ShapeModel } from '../../ShapeModel'

export class GroupModel extends ShapeModel<Konva.Group, Konva.ContainerConfig> {
  /**
   * @inheritdoc
   */
  public get type(): string {
    return 'group'
  }

  /**
   * @inheritdoc
   * @override
   */
  public rotate(theta: number) {
    rotateAroundCenter(this.node, theta)

    this.board.events.emit('shape:rotate', {
      shapes: [this]
    })
  }
}
