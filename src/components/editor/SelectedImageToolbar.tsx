import type <PERSON><PERSON><PERSON> from "@/components/pikaso";
import {
  Captions,
  Expand,
  Maximize,
  Replace as <PERSON>laceI<PERSON>,
  Shuffle,
  Trash2,
  <PERSON>Cir<PERSON>,
  Crop as CropIcon,
} from "lucide-react";
import type React from "react";
import { getAllCropPositions, getCropPositionDescription } from "@/components/pikaso/utils/image-crop-utils";
import type { ImageCropPosition } from "@/components/pikaso/shape/drawers/ImageDrawer";
import type { ImageModel } from "@/components/pikaso/shape/models/ImageModel";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { useState } from "react";

// Define SelectedImageToolbar component
export interface SelectedImageToolbarProps {
  editor: Pikaso | null; // Pikaso editor instance
  onDelete: () => void;
  onCaption: () => void;
  onReplace: () => void;
  onSwap: () => void;
  onScale: () => void;
  onFillPage: () => void;
  isMobile: boolean;
}

export const SelectedImageToolbar: React.FC<SelectedImageToolbarProps> = ({
  editor,
  onDelete,
  onCaption,
  onReplace,
  onSwap,
  onScale,
  onFillPage,
  isMobile,
}) => {
  const handleClose = () => {
    if (editor?.board?.selection) {
      editor.board.selection.deselectAll();
    }
  };

  const [cropPopoverOpen, setCropPopoverOpen] = useState(false);

  const handleCropSelect = (position: ImageCropPosition) => {
    if (!editor) return;
    const shape = editor.board?.selection?.list?.[0] as ImageModel | undefined;
    if (!shape || shape.type !== "image" || typeof shape.getCropPosition !== "function") {
      return;
    }

    shape.setCropPosition(position);
    shape.applyCropToSelf();
    editor.board.draw();
    setCropPopoverOpen(false);
  };

  const buttonClass =
    "flex flex-col items-center text-white hover:bg-gray-700 p-1 md:p-2 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500";
  const iconSize = isMobile ? 18 : 22;
  const textSize = isMobile ? "text-[10px]" : "text-xs";

  return (
    <div className="bg-gray-800 p-1 md:p-2 flex justify-around items-center shadow-md">
      <div className="flex justify-start items-center space-x-1 md:space-x-2 overflow-x-auto">
        <button
          type="button"
          onClick={onDelete}
          className={buttonClass}
          title="删除"
        >
          <Trash2 size={iconSize} />
          <span className={`${textSize} mt-1`}>删除</span>
        </button>
        <button
          type="button"
          onClick={onCaption}
          className={buttonClass}
          title="字幕"
        >
          <Captions size={iconSize} />
          <span className={`${textSize} mt-1`}>字幕</span>
        </button>
        <button
          type="button"
          onClick={onReplace}
          className={buttonClass}
          title="替换"
        >
          <ReplaceIcon size={iconSize} />
          <span className={`${textSize} mt-1`}>替换</span>
        </button>
        <button
          type="button"
          onClick={onSwap}
          className={buttonClass}
          title="交换"
        >
          <Shuffle size={iconSize} />
          <span className={`${textSize} mt-1`}>交换</span>
        </button>
        <button
          type="button"
          onClick={onScale}
          className={buttonClass}
          title="缩放"
        >
          <Expand size={iconSize} />
          <span className={`${textSize} mt-1`}>缩放</span>
        </button>
        <button
          type="button"
          onClick={onFillPage}
          className={buttonClass}
          title="填充"
        >
          <Maximize size={iconSize} />
          <span className={`${textSize} mt-1`}>填充</span>
        </button>
        <Popover open={cropPopoverOpen} onOpenChange={setCropPopoverOpen}>
          <PopoverTrigger asChild>
            <button
              type="button"
              className={buttonClass}
              title="对齐"
            >
              <CropIcon size={iconSize} />
              <span className={`${textSize} mt-1`}>对齐</span>
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-2" align="start">
            <div className="grid grid-cols-3 gap-1">
              {getAllCropPositions().map((pos) => {
                const desc = getCropPositionDescription(pos as ImageCropPosition);
                return (
                  <button
                    key={pos}
                    type="button"
                    onClick={() => handleCropSelect(pos as ImageCropPosition)}
                    className="w-10 h-10 flex items-center justify-center rounded border bg-gray-700 hover:bg-gray-600 text-xs text-white"
                    title={desc}
                  >
                    {desc.split("对齐")[0]}
                  </button>
                );
              })}
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <button
        type="button"
        onClick={handleClose}
        className="text-gray-300 hover:text-white p-1 md:p-2 rounded ml-2 md:ml-4 flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500"
        title="关闭工具栏"
      >
        <XCircle size={isMobile ? 16 : 18} />
        <span className={`${isMobile ? "hidden" : "inline"} ml-1 text-xs`}>
          关闭
        </span>
      </button>
    </div>
  );
};
