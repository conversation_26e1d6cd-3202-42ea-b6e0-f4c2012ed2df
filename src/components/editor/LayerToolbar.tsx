import {
  ArrowDown,
  ArrowDownToLine,
  ArrowUp,
  ArrowUpToLine,
} from "lucide-react"

interface LayerToolbarProps {
  onLayerChange: (action: "up" | "down" | "top" | "bottom") => void
}

export const LayerToolbar = ({ onLayerChange }: LayerToolbarProps) => {
  const layerTools = [
    {
      icon: ArrowUp,
      label: "上移",
      action: "up",
    },
    {
      icon: ArrowDown,
      label: "下移",
      action: "down",
    },
    {
      icon: ArrowUpToLine,
      label: "置顶",
      action: "top",
    },
    {
      icon: ArrowDownToLine,
      label: "置底",
      action: "bottom",
    },
  ] as const

  return (
    <div className="absolute top-4 left-1/2 -translate-x-1/2 z-10 bg-slate-800 text-white rounded-lg shadow-md p-2 flex items-center space-x-2">
      {layerTools.map((tool) => {
        const Icon = tool.icon
        return (
          <button
            key={tool.action}
            type="button"
            className="flex flex-col items-center space-y-1 rounded-lg p-2 transition-colors hover:bg-slate-700"
            onClick={() => onLayerChange(tool.action)}
            title={tool.label}
          >
            <Icon size={20} />
            <span className="text-xs whitespace-nowrap">{tool.label}</span>
          </button>
        )
      })}
    </div>
  )
}
