import { X } from "lucide-react"
import { Drawer } from "vaul"
import { stickers } from "./stickers"

interface StickerPanelProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onStickerSelect: (src: string) => void
}

export const StickerPanel = ({
  open,
  onOpenChange,
  onStickerSelect,
}: StickerPanelProps) => {
  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40" />
        <Drawer.Content className="bg-white h-[70vh] fixed bottom-0 left-0 right-0 outline-none rounded-t-xl">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">选择贴纸</h2>
              <button
                type="button"
                onClick={() => onOpenChange(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-4 overflow-y-auto space-y-4">
              {Object.entries(stickers).map(([group, items]) => (
                <div key={group}>
                  <h3 className="font-medium mb-2 text-gray-700">{group}</h3>
                  <div className="grid grid-cols-4 gap-3">
                    {items.map((src, idx) => (
                      <button
                        key={idx}
                        type="button"
                        className="aspect-square rounded-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-colors"
                        onClick={() => {
                          onStickerSelect(src)
                          onOpenChange(false)
                        }}
                      >
                        <img
                          src={src}
                          alt={`${group} ${idx + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  )
}