import { Button } from "@/components/ui/button"
import { Upload, X } from "lucide-react"
import { useRef } from "react"
import { Drawer } from "vaul"

interface ImagePanelProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImageSelect: (src: string) => void
}

export const ImagePanel = ({ open, onOpenChange, onImageSelect }: ImagePanelProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const images = [
    "https://images.unsplash.com/photo-1547981609-4b6bfe67ca0b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    "https://images.unsplash.com/photo-1513031300226-c8fb12de9ade?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    "https://images.unsplash.com/photo-1524413840807-0c3cb6fa808d?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    "https://images.unsplash.com/photo-1557683316-973673baf926?w=800",
    "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800",
    "https://images.unsplash.com/photo-1557682250-33bd709cbe85?w=800",
    "https://images.unsplash.com/photo-1557683304-673a23048d34?w=800",
    "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800",
    "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800",
  ]

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    const reader = new FileReader()
    reader.onload = (e) => {
      const src = e.target?.result as string
      onImageSelect(src)
    }
    reader.readAsDataURL(file)
    if (fileInputRef.current) fileInputRef.current.value = ""
  }

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40" />
        <Drawer.Content className="bg-white h-[70vh] fixed bottom-0 left-0 right-0 outline-none rounded-t-xl">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">选择图片</h2>
              <button type="button" onClick={() => onOpenChange(false)} className="p-1 hover:bg-gray-100 rounded">
                <X size={20} />
              </button>
            </div>
            <div className="p-4 overflow-y-auto">
              <input type="file" ref={fileInputRef} accept="image/*" onChange={handleFileUpload} style={{ display: "none" }} />
              <div className="mb-4">
                <Button variant="outline" className="w-full" onClick={() => fileInputRef.current?.click()}>
                  <Upload size={16} className="mr-2" />上传
                </Button>
              </div>
              <div className="grid grid-cols-3 gap-3">
                {images.map((src, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => onImageSelect(src)}
                    className="aspect-square rounded-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-colors"
                  >
                    <img src={src} alt={`图片 ${index + 1}`} className="w-full h-full object-cover" />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  )
}