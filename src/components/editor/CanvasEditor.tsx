import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all"
import { resize } from "motion"
import { forwardRef, useEffect } from "react"

interface CanvasEditorProps {
  editor: Pikaso | null
  canvasSize: { width: number; height: number }
  isMobile?: boolean
}

export const CanvasEditor = forwardRef<HTMLDivElement, CanvasEditorProps>(
  ({ editor, canvasSize, isMobile = false }, ref) => {
    // 计算画布宽高比
    const aspectRatio = canvasSize.height / canvasSize.width

    useEffect(() => {
      // 处理画布容器和Pikaso板的尺寸调整 - 仅在移动端启用
      if (
        !editor ||
        !ref ||
        typeof ref === "function" ||
        !ref.current ||
        !isMobile
      ) {
        return
      }

      const containerElement = ref.current

      const setSizeAndRescale = (currentWidth: number) => {
        if (currentWidth <= 0) {
          // 如果宽度无效，则跳过调整大小
          containerElement.style.height = "0px"
          return
        }
        const newHeight = currentWidth * aspectRatio
        containerElement.style.height = `${newHeight}px`
        // Pikaso的board.rescale()应在其容器div具有新尺寸后调用
        editor.board.rescale()
      }

      // 使用 motion 的 resize 观察器
      // 回调接收 (element, info: DOMRectReadOnly)
      const stopObserving = resize(containerElement, (_, info) => {
        console.log("🚀 ~ info:", info)
        setSizeAndRescale(info.width)
      })

      // 初始尺寸设置：
      // 组件已渲染，containerElement 应具有由CSS确定的初始宽度
      const initialWidth = containerElement.offsetWidth
      if (initialWidth > 0) {
        setSizeAndRescale(initialWidth)
      }

      return () => {
        // 清理观察器
        stopObserving()
      }
    }, [editor, ref, aspectRatio, isMobile])

    return (
      <div
        ref={ref}
        className={`canvas-container flex justify-center items-center bg-gray-100  ${
          isMobile ? "p-2" : "p-4"
        }`}
      >
        <div className="relative bg-white shadow-lg rounded-lg border border-gray-200 p-5" />
      </div>
    )
  },
)

CanvasEditor.displayName = "CanvasEditor"
