import type { ShapeModel } from "@/components/pikaso/index.all"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Circle,
  ImageIcon,
  Layers,
  Palette,
  Square,
  Sticker,
  Triangle,
  Type,
} from "lucide-react"
import { type ComponentType, useState } from "react"

interface BottomToolbarProps {
  onAddImage: () => void
  onAddText: () => void
  onAddShape: (type: "rect" | "circle" | "triangle") => void
  onOpenBackgrounds: () => void
  onOpenTemplates: () => void
  onOpenStickers: () => void
}

export const BottomToolbar = ({
  onAddImage,
  onAddText,
  onAddShape,
  onOpenBackgrounds,
  onOpenTemplates,
  onOpenStickers,
}: BottomToolbarProps) => {
  const [shapePopoverOpen, setShapePopoverOpen] = useState(false)

  const shapeItems: {
    icon: ComponentType<any>
    label: string
    type: "rect" | "circle" | "triangle"
  }[] = [
    { icon: Square, label: "矩形", type: "rect" },
    { icon: Circle, label: "圆形", type: "circle" },
    { icon: Triangle, label: "三角形", type: "triangle" },
  ]

  type Tool = {
    icon: ComponentType<any>
    label: string
    onClick: () => void
    isShape?: boolean
  }

  const primaryTools: Tool[] = [
    {
      icon: ImageIcon,
      label: "图片",
      onClick: onAddImage,
    },
    {
      icon: Type,
      label: "文字",
      onClick: onAddText,
    },
    {
      icon: Square,
      label: "形状",
      onClick: () => setShapePopoverOpen(true),
      isShape: true,
    },
    {
      icon: Layers,
      label: "背景",
      onClick: onOpenBackgrounds,
    },
    {
      icon: Palette,
      label: "模板",
      onClick: onOpenTemplates,
    },
    {
      icon: Sticker,
      label: "贴纸",
      onClick: onOpenStickers,
    },
  ]

  return (
    <div className="w-screen overflow-x-auto bg-slate-800 text-white">
      <div className="inline-flex items-center space-x-2 px-2 py-3">
        {primaryTools.map((item, index) => {
          const Icon = item.icon
          if (item.isShape) {
            return (
              <Popover
                key={index}
                open={shapePopoverOpen}
                onOpenChange={setShapePopoverOpen}
              >
                <PopoverTrigger asChild>
                  <button
                    type="button"
                    className="flex flex-shrink-0 flex-col items-center space-y-1 rounded-lg p-2 transition-colors hover:bg-slate-700"
                  >
                    <Icon size={24} className="flex-shrink-0" />
                    <span className="whitespace-nowrap text-xs">
                      {item.label}
                    </span>
                  </button>
                </PopoverTrigger>

                <PopoverContent
                  side="top"
                  align="center"
                  className="rounded-md bg-white p-2 shadow-md"
                >
                  <div className="grid grid-cols-3 gap-4">
                    {shapeItems.map(({ icon: IconShape, label, type }, i) => (
                      <button
                        key={i}
                        type="button"
                        className="flex flex-col items-center space-y-1 rounded-md p-2 hover:bg-gray-100"
                        onClick={() => {
                          onAddShape(type)
                          setShapePopoverOpen(false)
                        }}
                      >
                        <IconShape size={24} className="text-gray-700" />
                        <span className="text-xs text-gray-700">{label}</span>
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            )
          }

          return (
            <button
              key={index}
              type="button"
              className="flex flex-shrink-0 flex-col items-center space-y-1 rounded-lg p-2 transition-colors hover:bg-slate-700"
              onClick={item.onClick}
            >
              <Icon size={24} className="flex-shrink-0" />
              <span className="whitespace-nowrap text-xs">{item.label}</span>
            </button>
          )
        })}
      </div>
    </div>
  )
}
