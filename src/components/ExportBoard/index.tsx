import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";
import { useState } from "react";

import type { Pi<PERSON><PERSON> } from "@/components/pikaso/index.all";

import { BasePopover } from "../BasePopover";

interface Props {
  editor: Pikaso | null;
}

export function ExportBoard({ editor }: Props) {
  const [result, setResult] = useState<{
    type: "image" | "json";
    data: string;
  } | null>(null);

  const handleExportImage = () => {
    if (!editor) {
      return;
    }

    const image = editor.export.toImage({
      pixelRatio: 3,
    });

    setResult({
      type: "image",
      data: image,
    });
  };

  const handleExportJson = () => {
    if (!editor) {
      return;
    }

    const json = editor.export.toJson();
    setResult({
      type: "json",
      data: JSON.stringify(json),
    });
  };

  return (
    <>
      <BasePopover
        buttonRenderer={({ id, isOpen }) => (
          <Button
            aria-describedby={id}
            size="sm"
            variant="outline"
            className={`min-w-0 px-2 ${isOpen ? "border-primary text-primary" : ""}`}
          >
            <span className="font-medium">导出</span>
          </Button>
        )}
      >
        {({ close }) => (
          <div className="flex p-4 space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                close();
                handleExportImage();
              }}
            >
              图片
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                close();
                handleExportJson();
              }}
            >
              JSON
            </Button>
          </div>
        )}
      </BasePopover>

      <Dialog open={!!result} onOpenChange={(open) => !open && setResult(null)}>
        {result && (
          <>
            <DialogHeader>
              <DialogTitle className="flex justify-between items-center">
                导出为 {result.type === "image" ? "图片" : "JSON"}
                <DialogClose className="h-6 w-6 rounded-full opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none">
                  <X className="h-4 w-4" />
                  <span className="sr-only">关闭</span>
                </DialogClose>
              </DialogTitle>
            </DialogHeader>

            <DialogContent className="overflow-hidden sm:max-w-[800px]">
              {result.type === "image" && (
                <div className="w-full">
                  <img
                    src={result.data}
                    className="border border-gray-300 rounded-md max-w-[500px] mx-auto block"
                    alt="导出图片"
                  />
                </div>
              )}

              {result.type === "json" && (
                <Textarea
                  value={result.data ?? ""}
                  rows={15}
                  className="font-mono text-sm w-full"
                  readOnly
                />
              )}
            </DialogContent>
          </>
        )}
      </Dialog>
    </>
  );
}
