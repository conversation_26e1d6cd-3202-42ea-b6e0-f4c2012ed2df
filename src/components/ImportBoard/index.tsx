import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all";
import { type ChangeEvent, useState } from "react";

import { BasePopover } from "../BasePopover";

interface Props {
  editor: <PERSON><PERSON><PERSON> | null;
}

export function ImportBoard({ editor }: Props) {
  const [value, setValue] = useState("");
  const { toast } = useToast();

  const onChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };

  const handleImport = () => {
    if (!editor) {
      return;
    }

    try {
      JSON.parse(value);
    } catch (e) {
      toast({
        variant: "destructive",
        title: "错误",
        description: "无效的 JSON 格式",
      });
      return;
    }

    editor.reset();

    setTimeout(() => {
      editor.load(value);
    }, 1000);
  };

  return (
    <>
      <BasePopover
        buttonRenderer={({ id, isOpen }) => (
          <Button
            aria-describedby={id}
            size="sm"
            variant="outline"
            className={`min-w-0 px-2 ${isOpen ? "border-primary text-primary" : ""}`}
          >
            <span className="font-medium">导入</span>
          </Button>
        )}
      >
        {({ close }) => (
          <div className="p-4 flex flex-col space-y-4">
            <div>
              <label
                htmlFor="json-input"
                className="block text-sm font-medium mb-1"
              >
                JSON 文本
              </label>
              <Textarea
                id="json-input"
                rows={10}
                className="w-[300px]"
                onChange={onChange}
                placeholder="粘贴 JSON 数据"
              />
            </div>

            <Button
              variant="outline"
              className="mt-2"
              disabled={!value}
              onClick={() => {
                close();
                handleImport();
              }}
            >
              导入
            </Button>
          </div>
        )}
      </BasePopover>

      {/* 使用 useToast 钩子替代 Snackbar */}
    </>
  );
}
