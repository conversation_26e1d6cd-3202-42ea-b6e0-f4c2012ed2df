import type { Album, AlbumFormat } from "../types/album"

// 模拟数据
const mockAlbums: Album[] = [
  {
    id: "1",
    title: "China",
    coverImage:
      "https://images.unsplash.com/photo-1547981609-4b6bfe67ca0b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    description: "Photo Book, Square 30x30cm, Hardcover, Matt Paper, 25 Pages",
    format: {
      size: "30x30cm",
      type: "Hardcover",
      paperType: "Matt Paper",
      pageCount: 25,
    },
    createdAt: "2023-10-15",
  },
  {
    id: "2",
    title: "China 2023",
    coverImage:
      "https://images.unsplash.com/photo-1513031300226-c8fb12de9ade?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    description:
      "Photo Book, Portrait 21x30cm, Hardcover, Matt Paper, 25 Pages",
    format: {
      size: "21x30cm",
      type: "Hardcover",
      paperType: "Matt Paper",
      pageCount: 25,
    },
    createdAt: "2023-11-20",
  },
  {
    id: "3",
    title: "China 2023",
    coverImage:
      "https://images.unsplash.com/photo-1524413840807-0c3cb6fa808d?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    description:
      "Photo Book, Landscape 30x21cm, Hardcover, Matt Paper, 25 Pages",
    format: {
      size: "30x21cm",
      type: "Hardcover",
      paperType: "Matt Paper",
      pageCount: 25,
    },
    createdAt: "2023-12-10",
  },
]

// 获取相册列表
export const getAlbums = async (): Promise<Album[]> => {
  // 模拟API请求延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockAlbums)
    }, 800)
  })
}

// 获取单个相册
export const getAlbumById = async (id: string): Promise<Album | undefined> => {
  // 模拟API请求延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      const album = mockAlbums.find((album) => album.id === id)
      resolve(album)
    }, 500)
  })
}

// 根据格式获取相册
export const getAlbumsByFormat = async (
  format: AlbumFormat,
): Promise<Album[]> => {
  // 模拟API请求延迟
  return new Promise((resolve) => {
    setTimeout(() => {
      const filteredAlbums = mockAlbums.filter((album) => {
        if (format === "square") return album.format.size.includes("30x30")
        if (format === "portrait") return album.format.size.includes("21x30")
        if (format === "landscape") return album.format.size.includes("30x21")
        return true
      })
      resolve(filteredAlbums)
    }, 500)
  })
}
