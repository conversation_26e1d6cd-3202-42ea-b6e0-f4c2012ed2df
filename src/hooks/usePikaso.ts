import { type RefObject, useEffect, useRef, useState } from "react"

import {
  type BaseShapes,
  Pikaso,
  type RegisterShapesFn,
  type Settings,
} from "@/components/pikaso/index.all"

export default function usePikaso<T extends BaseShapes = BaseShapes>(
  options: Partial<Settings> = {},
  registerShapes?: RegisterShapesFn<T>,
): [RefObject<HTMLDivElement>, Pikaso<T> | null] {
  const [instance, setInstance] = useState<Pikaso<T> | null>(null)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const editor = new Pikaso<T>(
      {
        container: ref.current as HTMLDivElement,
        ...options,
      },
      registerShapes,
    )

    setInstance(editor)
  }, [])

  return [ref, instance]
}
