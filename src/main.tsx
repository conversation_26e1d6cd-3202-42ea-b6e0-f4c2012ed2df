import { StrictMode } from "react"
import { createRoot } from "react-dom/client"

import { EntryApp } from "@/entry-app"
import { EntryAppProviders } from "@/providers/entry-app-providers"

import "@/main.css"

const rootElement = document.getElementById("app")

if (!rootElement) {
  throw new Error("Document root not found")
}

const root = createRoot(rootElement)
root.render(
  <StrictMode>
    <EntryAppProviders>
      <EntryApp />
    </EntryAppProviders>
  </StrictMode>,
)

// // ---------------- Stagewise Dev Toolbar ----------------
// // 仅在开发模式启用，且使用独立 React root 避免干扰主应用
// if (import.meta.env.DEV) {
//   // 动态导入以免进入生产 bundle
//   import("@stagewise/toolbar-react").then(({ StagewiseToolbar }) => {
//     const stagewiseRootElementId = "stagewise-toolbar-root"
//     let stagewiseRootElement = document.getElementById(stagewiseRootElementId)

//     if (!stagewiseRootElement) {
//       stagewiseRootElement = document.createElement("div")
//       stagewiseRootElement.id = stagewiseRootElementId
//       document.body.appendChild(stagewiseRootElement)
//     }

//     const stagewiseRoot = createRoot(stagewiseRootElement)

//     const stagewiseConfig = {
//       plugins: [],
//     }

//     stagewiseRoot.render(<StagewiseToolbar config={stagewiseConfig} />)
//   })
// }
