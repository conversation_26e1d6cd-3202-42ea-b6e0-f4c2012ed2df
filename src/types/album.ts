export interface Page {
  id: string
  index: number // 实际页码
  type: "cover" | "content" | "back-cover" // 页面类型
  content: PageContent
  thumbnail?: string
  metadata: PageMetadata
}

export interface PageContent {
  stage: {
    attrs: { width: number; height: number; x: number; y: number }
    filters: unknown[]
    className: string
  }
  layer: {
    attrs: { x: number; y: number; width: number; height: number }
    filters: unknown[]
    className: string
  }
  background: {
    image: {
      attrs: { x: number; y: number }
      filters: unknown[]
      className: string
      zIndex: number
    }
    overlay: {
      attrs: { x: number; y: number }
      filters: unknown[]
      className: string
      zIndex: number
    }
  }
  shapes: PageShape[]
}

export interface PageShape {
  attrs: Record<string, unknown>
  filters: unknown[]
  className: string
  zIndex: number
  children?: PageShape[]
}

export interface PageMetadata {
  createdAt: number
  updatedAt: number
  version: string
}

export interface Album {
  id: string
  name: string
  pages: Page[]
  currentPageId: string | null
  metadata: AlbumMetadata
}

export interface AlbumMetadata {
  createdAt: number
  updatedAt: number
  version: string
  totalPages: number
  template?: string
  theme?: string
}

export type AlbumTemplate = "standard" | "premium" | "custom"

export type AlbumSize = "small" | "medium" | "large" | "custom"

export type AlbumFormat = "square" | "portrait" | "landscape"
