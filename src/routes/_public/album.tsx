import { AlbumManager } from "@/components/album/AlbumManager"
import { useAlbumStore } from "@/stores/albumStore"
import { createFileRoute } from "@tanstack/react-router"
import { useEffect } from "react"

export const Route = createFileRoute("/_public/album")({
  component: RouteComponent,
})

function RouteComponent() {
  const { importAlbum } = useAlbumStore()

  useEffect(() => {
    // importAlbum()
  }, [importAlbum])

  return <AlbumManager />
}
