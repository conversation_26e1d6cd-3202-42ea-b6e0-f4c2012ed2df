import PageThumbnail from "@/components/album/PageThumbnail"
import { <PERSON><PERSON> } from "@/components/ui/button"
import type { Page, PageContent } from "@/types/album"
import { createFileRoute } from "@tanstack/react-router"
import { useNavigate } from "@tanstack/react-router"
import {
  ArrowLeft,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  RotateCw,
  Share2,
} from "lucide-react"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"

export const Route = createFileRoute("/_public/preview")({
  component: AlbumPreviewPage,
})

// Mock data
const baseWidth = 500
const baseHeight = 500

const createMockPageContent = (
  w: number,
  h: number,
  seedText: string,
): PageContent => ({
  stage: {
    attrs: { width: 500, height: 500, x: 0, y: 0 },
    filters: [],
    className: "Stage",
  },
  layer: {
    attrs: { x: 0, y: 0, width: 500, height: 500 },
    filters: [],
    className: "Layer",
  },
  background: {
    image: {
      attrs: { x: 0, y: 0 },
      filters: [],
      className: "Image",
      zIndex: 0,
    },
    overlay: {
      attrs: { x: 0, y: 0 },
      filters: [],
      className: "Rect",
      zIndex: 1,
    },
  },
  shapes: [
    {
      attrs: {
        fill: "red",
        radius: 50,
        x: 500,
        y: 100,
        width: 100,
        height: 100,
      },
      filters: [],
      className: "Circle",
      zIndex: 4,
    },
    {
      attrs: { x: 600, y: 300, width: 407.81, height: 40 },
      filters: [],
      className: "Label",
      zIndex: 5,
      children: [
        {
          attrs: { fill: "transparent", width: 407.8125, height: 40 },
          filters: [],
          className: "Tag",
          zIndex: 0,
        },
        {
          attrs: {
            text: "Pikaso is great, isn't it?",
            fontSize: 40,
            fontWeight: "bold",
            fill: "purple",
            height: "auto",
          },
          filters: [],
          className: "Text",
          zIndex: 0,
        },
      ],
    },
    {
      attrs: {
        width: 250,
        height: 250,
        x: 100,
        y: 100,
        url: "https://unicorn-media.ancda.com/prod/growthArchives/78492111288139808/parent/88357351911850006/2025-05-09/rmGcEYKs.jpg",
      },
      filters: [],
      className: "Image",
      zIndex: 6,
    },
  ],
})

const mockCanvasData: Page[] = [
  {
    id: "cover-id",
    index: 0,
    type: "cover",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "cover"),
  },
  {
    id: "page-1-id",
    index: 1,
    type: "content",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "page1"),
  },
  {
    id: "page-2-id",
    index: 2,
    type: "content",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "page2"),
  },
  {
    id: "page-3-id",
    index: 3,
    type: "content",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "page3"),
  },
  {
    id: "page-4-id",
    index: 4,
    type: "content",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "page4"),
  },
  {
    id: "page-5-id",
    index: 5,
    type: "content",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "page5"),
  },
  {
    id: "back-cover-id",
    index: 999,
    type: "back-cover",
    metadata: { createdAt: Date.now(), updatedAt: Date.now(), version: "1" },
    content: createMockPageContent(baseWidth, baseHeight, "backcover"),
  },
]

// 处理页面数据，转换为预览需要的格式
type PreviewItem = {
  id: string
  type: "cover" | "spread" | "back-cover"
  leftPage: Page | null
  rightPage: Page | null
  label: string
}

function processPages(pages: Page[]): PreviewItem[] {
  const result: PreviewItem[] = []

  // 找出封面、封底和内容页
  const coverPage = pages.find((page) => page.type === "cover")
  const backCoverPage = pages.find((page) => page.type === "back-cover")
  const contentPages = pages
    .filter((page) => page.type === "content")
    .sort((a, b) => a.index - b.index)

  // 添加封面
  if (coverPage) {
    result.push({
      id: `preview-cover-${coverPage.id}`,
      type: "cover",
      leftPage: null,
      rightPage: coverPage,
      label: "封面",
    })
  }

  // 第一个展开页：左侧为空白，右侧为第一页
  if (contentPages.length > 0) {
    result.push({
      id: `preview-spread-first-${contentPages[0].id}`,
      type: "spread",
      leftPage: null, // 空白页（封面背面）
      rightPage: contentPages[0],
      label: "第 1 页",
    })
  }

  // 后续展开页，每两页组成一个展开页（第2-3页，第4-5页...）
  for (let i = 1; i < contentPages.length; i += 2) {
    const leftPage = contentPages[i]
    const rightPage = i + 1 < contentPages.length ? contentPages[i + 1] : null

    result.push({
      id: `preview-spread-${i}-${leftPage.id}-${rightPage?.id || "empty"}`,
      type: "spread",
      leftPage: leftPage,
      rightPage: rightPage,
      label: rightPage
        ? `第 ${leftPage.index}-${rightPage.index} 页`
        : `第 ${leftPage.index} 页`,
    })
  }

  // 添加封底
  if (backCoverPage) {
    result.push({
      id: `preview-back-cover-${backCoverPage.id}`,
      type: "back-cover",
      leftPage: null,
      rightPage: backCoverPage,
      label: "封底",
    })
  }

  return result
}

function AlbumPreviewPage() {
  const navigate = useNavigate()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPortrait, setIsPortrait] = useState(false)
  const [visiblePages, setVisiblePages] = useState<Set<number>>(
    new Set([0, 1, 2]),
  )
  const [touchStartX, setTouchStartX] = useState<number | null>(null)
  const [touchDirection, setTouchDirection] = useState<"left" | "right" | null>(
    null,
  )
  const [isAnimating, setIsAnimating] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const mainContainerRef = useRef<HTMLDivElement>(null)

  // 处理页面数据
  const previewItems = useMemo(() => processPages(mockCanvasData), [])

  // 模拟数据加载
  useEffect(() => {
    // 模拟加载过程
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // 检测设备方向
  useEffect(() => {
    const checkOrientation = () => {
      setIsPortrait(window.innerHeight > window.innerWidth)
    }

    checkOrientation()
    window.addEventListener("resize", checkOrientation)

    return () => {
      window.removeEventListener("resize", checkOrientation)
    }
  }, [])

  // 预加载管理
  useEffect(() => {
    const newVisiblePages = new Set<number>([currentIndex])

    // 前后各预加载2页
    for (
      let i = Math.max(0, currentIndex - 2);
      i <= Math.min(previewItems.length - 1, currentIndex + 2);
      i++
    ) {
      newVisiblePages.add(i)
    }

    setVisiblePages(newVisiblePages)
  }, [currentIndex, previewItems.length])

  // 翻页处理 - 使用useCallback包装
  const goToNextPage = useCallback(() => {
    if (currentIndex < previewItems.length - 1 && !isAnimating) {
      setIsAnimating(true)
      setCurrentIndex(currentIndex + 1)
      setTimeout(() => setIsAnimating(false), 300) // 动画持续时间
    }
  }, [currentIndex, previewItems.length, isAnimating])

  const goToPrevPage = useCallback(() => {
    if (currentIndex > 0 && !isAnimating) {
      setIsAnimating(true)
      setCurrentIndex(currentIndex - 1)
      setTimeout(() => setIsAnimating(false), 300) // 动画持续时间
    }
  }, [currentIndex, isAnimating])

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowRight" || e.key === " ") {
        e.preventDefault()
        goToNextPage()
      } else if (e.key === "ArrowLeft") {
        e.preventDefault()
        goToPrevPage()
      } else if (e.key === "Home") {
        e.preventDefault()
        setCurrentIndex(0)
      } else if (e.key === "End") {
        e.preventDefault()
        setCurrentIndex(previewItems.length - 1)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [goToNextPage, goToPrevPage, previewItems.length])

  // 触摸滑动支持
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX)
    setTouchDirection(null)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartX === null) return

    const currentX = e.touches[0].clientX
    const diff = touchStartX - currentX

    // 确定滑动方向
    if (diff > 50 && touchDirection !== "left") {
      setTouchDirection("left") // 向左滑，前进
    } else if (diff < -50 && touchDirection !== "right") {
      setTouchDirection("right") // 向右滑，后退
    }
  }

  const handleTouchEnd = () => {
    if (touchDirection === "left") {
      goToNextPage()
    } else if (touchDirection === "right") {
      goToPrevPage()
    }

    setTouchStartX(null)
    setTouchDirection(null)
  }

  // 分享功能
  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: "我的相册",
          text: "查看我的相册",
          url: window.location.href,
        })
        .catch(console.error)
    } else {
      navigator.clipboard
        .writeText(window.location.href)
        .then(() => alert("链接已复制到剪贴板"))
        .catch(() => alert("分享功能暂不可用"))
    }
  }

  // 订购功能
  const handleOrder = () => {
    console.log("准备订购相册")
    alert("订购功能即将上线")
  }

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {isLoading && (
        <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-gray-200 border-t-purple-600 rounded-full animate-spin" />
            <p className="mt-4 text-gray-600">加载相册中...</p>
          </div>
        </div>
      )}

      {/* 顶部导航 */}
      <div className="bg-gray-800 text-white px-4 py-3">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate({ to: "/" })}
            className="text-white hover:bg-gray-700"
          >
            <ArrowLeft size={16} className="mr-2" />
            返回
          </Button>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="bg-purple-600 text-white border-purple-600 hover:bg-purple-700"
            >
              <Share2 size={16} className="mr-1" />
              分享
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleOrder}
              className="bg-white text-gray-800 hover:bg-gray-100"
            >
              订购
            </Button>
          </div>
        </div>
      </div>

      {/* 主内容区 */}
      <div
        ref={mainContainerRef}
        className="flex-1 flex flex-col items-center justify-center md:justify-center p-2 pt-1 md:p-4 relative overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {isPortrait && (
          <div className="mb-1 md:mb-2 flex items-center justify-center text-gray-500 bg-gray-100 p-1 rounded text-xs">
            <RotateCw size={14} className="mr-1" />
            <span>旋转设备获得更佳体验</span>
          </div>
        )}

        <div className="relative w-full aspect-[2/1] max-w-4xl ">
          {previewItems.map((item, index) => (
            <div
              key={item.id}
              className={`transition-all duration-300 absolute top-0 left-0 w-full ${
                index === currentIndex
                  ? "opacity-100 z-10 transform-none"
                  : index < currentIndex
                    ? "opacity-0 z-0 transform -translate-x-full"
                    : "opacity-0 z-0 transform translate-x-full"
              }`}
              style={{
                display: visiblePages.has(index) ? "block" : "none",
                transition:
                  "opacity 300ms ease-in-out, transform 300ms ease-in-out",
              }}
            >
              {(item.type === "cover" || item.type === "back-cover") && (
                <PreviewCoverPage page={item.rightPage} type={item.type} />
              )}

              {item.type === "spread" && (
                <PreviewSpread
                  leftPage={item.leftPage}
                  rightPage={item.rightPage}
                />
              )}
            </div>
          ))}
        </div>

        {/* 翻页控制 */}
        <div className="flex justify-between  pointer-events-none w-full mt-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={goToPrevPage}
            disabled={currentIndex === 0 || isAnimating}
            className={`bg-white/80 text-gray-800 shadow-md hover:bg-white pointer-events-auto transition-opacity ${
              currentIndex === 0 ? "opacity-50" : "opacity-100"
            }`}
          >
            <ChevronLeft size={24} />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={goToNextPage}
            disabled={currentIndex === previewItems.length - 1 || isAnimating}
            className={`bg-white/80 text-gray-800 shadow-md hover:bg-white pointer-events-auto transition-opacity ${
              currentIndex === previewItems.length - 1
                ? "opacity-50"
                : "opacity-100"
            }`}
          >
            <ChevronRight size={24} />
          </Button>
        </div>
      </div>

      {/* 底部导航 */}
      <div className="bg-white border-t border-gray-200 py-3 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-2">
            <div className="text-sm text-gray-500">
              {currentIndex + 1} / {previewItems.length}
            </div>

            <div className="flex space-x-1">
              {previewItems.map((item, index) => (
                <button
                  key={item.id}
                  type="button"
                  onClick={() => setCurrentIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentIndex
                      ? "bg-purple-600 scale-125"
                      : "bg-gray-300"
                  }`}
                  aria-label={`跳转到第 ${index + 1} 页`}
                />
              ))}
            </div>

            <div className="text-sm text-gray-500">
              {previewItems[currentIndex]?.label || ""}
            </div>
          </div>

          {/* 缩略图导航 */}
          <div className="overflow-x-auto p-1 hide-scrollbar">
            <div className="flex space-x-2 min-w-max">
              {previewItems.map((item, index) => (
                <ThumbnailNav
                  key={item.id}
                  item={item}
                  index={index}
                  isActive={index === currentIndex}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 页面渲染器组件
function PageRenderer({ page, label }: { page: Page | null; label?: string }) {
  if (!page)
    return (
      <div className="flex items-center justify-center h-full bg-gray-50 rounded border border-gray-200">
        <span className="text-gray-400 text-xs md:text-sm">空白页</span>
      </div>
    )

  return (
    <div className="relative w-full h-full">
      <div className="bg-white shadow-lg overflow-hidden h-full">
        <PageThumbnail page={page} />
      </div>
      {label && (
        <div className="absolute bottom-1 md:bottom-2 right-1 md:right-2 text-xs font-medium text-gray-500 bg-white/80 px-1 md:px-1.5 py-0.5 rounded shadow-sm">
          {label}
        </div>
      )}
    </div>
  )
}

// 封面和封底预览组件
function PreviewCoverPage({
  page,
  type,
}: { page: Page | null; type: "cover" | "back-cover" }) {
  if (!page) return null

  const label = type === "cover" ? "封面" : "封底"

  return (
    <div className="flex flex-col items-center w-full">
      <div className="w-full max-w-4xl mx-auto bg-white/50 rounded-md shadow-sm overflow-hidden p-0.5 md:p-1">
        <div className="flex justify-center">
          <div className="aspect-square w-1/2 max-w-xs md:max-w-sm">
            <PageRenderer page={page} label={label} />
          </div>
        </div>
      </div>
    </div>
  )
}

// 双页展开预览组件
function PreviewSpread({
  leftPage,
  rightPage,
}: { leftPage: Page | null; rightPage: Page | null }) {
  return (
    <div className="flex flex-col w-full">
      <div className="w-full max-w-4xl mx-auto bg-white/50 rounded-md shadow-sm overflow-hidden p-0.5 md:p-1">
        <div className="grid grid-cols-2 max-w-2xl mx-auto">
          {/* 左页（含右侧渐变阴影） */}
          <div className="relative aspect-square overflow-hidden">
            <PageRenderer
              page={leftPage}
              label={leftPage ? `第 ${leftPage.index} 页` : undefined}
            />
            <div className="absolute inset-y-0 right-0 w-1/6 bg-gradient-to-l from-black/40  to-transparent pointer-events-none" />
          </div>

          {/* 右页（含左侧渐变阴影） */}
          <div className="relative aspect-square overflow-hidden">
            <PageRenderer
              page={rightPage}
              label={rightPage ? `第 ${rightPage.index} 页` : undefined}
            />
            <div className="absolute inset-y-0 left-0 w-1/6 bg-gradient-to-r from-black/40 to-transparent pointer-events-none" />
          </div>
        </div>
      </div>
    </div>
  )
}

// 封底预览组件已与封面组件合并为 PreviewCoverPage

// 缩略图导航组件
interface ThumbnailNavProps {
  item: PreviewItem
  index: number
  isActive: boolean
  onClick: () => void
}

function ThumbnailNav({ item, index, isActive, onClick }: ThumbnailNavProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`flex flex-col justify-center items-center transition-all ${
        isActive ? "transform scale-105" : "opacity-70 hover:opacity-100"
      }`}
    >
      <div
        className={`w-16 h-16 bg-white rounded overflow-hidden border ${
          isActive ? "border-purple-600 shadow-md" : "border-gray-200"
        }`}
      >
        {item.type === "spread" ? (
          <div className="grid grid-cols-2 h-full">
            <div className="relative overflow-hidden">
              {item.leftPage && (
                <div className="absolute inset-0  top-1/2 transform -translate-y-1/2">
                  <PageThumbnail page={item.leftPage} />
                </div>
              )}
            </div>
            <div className="relative overflow-hidden">
              {item.rightPage && (
                <div className="absolute inset-0  top-1/2 transform -translate-y-1/2">
                  <PageThumbnail page={item.rightPage} />
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="h-full">
            <PageThumbnail page={item.rightPage} />
          </div>
        )}
      </div>
    </button>
  )
}
