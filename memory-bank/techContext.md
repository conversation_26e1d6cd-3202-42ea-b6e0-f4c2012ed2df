# 技术上下文

## 使用的技术
- **前端框架**：React.js，用于构建用户界面。
- **路由库**：TanStack Router，用于管理应用路由。
- **状态管理**：TanStack Query，用于数据获取和缓存。
- **UI 组件库**：shadcn/ui，提供可重用的 UI 组件。
- **样式处理**：Tailwind CSS，用于快速样式设计。
- **构建工具**：Vite，用于快速开发和构建。
- **测试框架**：Vitest，用于单元测试；Playwright，用于端到端测试。
- **代码格式化**：Biome，确保代码风格一致。

## 开发设置
- **包管理器**：pnpm，用于高效的依赖管理。
- **开发环境**：VSCode，推荐使用与项目配置兼容的插件。
- **配置文件**：包括 `tsconfig.json`（TypeScript 配置）、`vite.config.ts`（Vite 配置）、`tailwind.config.ts`（Tailwind CSS 配置）等。
- **环境变量**：通过 `.env.example` 文件提供模板，开发者需根据实际情况配置。

## 技术约束
- **浏览器兼容性**：支持现代浏览器（如 Chrome、Firefox、Safari 的最新版本）。
- **性能要求**：优化加载时间和运行时性能，使用代码分割和懒加载。
- **TypeScript**：项目使用 TypeScript，确保类型安全和代码质量。

## 依赖项
- 核心依赖包括 React、TanStack Router、TanStack Query、Tailwind CSS 等，具体版本见 `package.json`。
- 开发依赖包括 Vite、Vitest、Playwright、Prettier 等。
- 核心功能依赖 Pikaso，这是一个基于 Konva 的画布编辑器库，本项目对 Pikaso 做了一些修改，并 fork 到本地 /src/components/pikaso，相关文档放在目录 /docs/Pikaso

## 工具使用模式
- **开发服务器**：使用 `pnpm dev` 启动 Vite 开发服务器。
- **测试**：使用 `pnpm test` 运行单元测试，`pnpm e2e` 运行端到端测试。
- **构建**：使用 `pnpm build` 构建生产版本。
- **代码格式化**：使用 `pnpm format` 格式化代码。
