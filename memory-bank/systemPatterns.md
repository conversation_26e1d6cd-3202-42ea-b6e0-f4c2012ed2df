# 系统模式和标准

## 系统架构
- **前端框架**：基于 React 或类似框架，构建单页应用（SPA）。
- **路由管理**：使用 TanStack Router，支持嵌套路由和动态路由。
- **模块化设计**：组件、路由和服务按功能模块组织，支持独立开发和测试。
- 
## 关键技术决策
- **TanStack Router**：选择此库以提供强大的路由功能，适应复杂应用需求。
- **组件库**：集成 shadcn/ui 或类似库，提供一致的 UI 元素。
- **状态管理**：使用 TanStack Query 进行数据获取和缓存，可能结合其他状态管理工具。
- **国际化与主题**：通过配置文件和提供者支持多语言和主题切换。

## 设计模式
- **组件化开发**：UI 拆分为可重用组件，提高代码复用率。
- **容器与展示分离**：逻辑处理与 UI 展示分离，提升代码可读性和维护性。
- **钩子模式**：使用 React Hooks 管理状态和副作用。
- **路由驱动开发**：路由定义驱动页面结构和数据加载。

## 代码规范

### 命名约定
- 组件：PascalCase (例如：`UserProfile.tsx`)
- 工具函数：camelCase (例如：`formatDate.ts`)
- 常量：UPPER_SNAKE_CASE (例如：`MAX_ITEMS = 10`)
- 接口：首字母 I 前缀 (例如：`IUser`)

### 文件结构
```
src/
  components/    # 可复用组件
  routes/        # 页面级组件和路由
  hooks/         # 自定义 Hooks
  utils/         # 工具函数
  types/         # 类型定义
  api/           # API 调用
  assets/        # 静态资源
  styles/        # 全局样式
```

## 组件模式

### 函数式组件
```tsx
import React from 'react';

interface IComponentProps {
  // 组件属性定义
}

export const Component: React.FC<IComponentProps> = ({ ... }) => {
  // 组件逻辑
  
  return (
    // JSX
  );
};
```

## 状态管理
- 使用 React Hooks 进行组件状态管理
- 对于跨组件状态，使用 Zustand
- 服务端状态管理使用 TanStack Query

## 样式指南
- 使用 Tailwind CSS 实用类
- 自定义样式放在 `styles` 目录下
- 组件特定样式使用 CSS Modules 或 styled-components
