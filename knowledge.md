# TanStack Router Boilerplate - Project Knowledge

## Project Overview

This is a modern React boilerplate project built with TanStack Router, designed for rapid prototyping and development. The project is currently a work in progress and serves as an opinionated starting point for new React applications.

## Tech Stack

### Core Technologies
- **React 19** - Latest React version with React Compiler enabled
- **TypeScript** - Full type safety throughout the project
- **Vite** - Fast build tool and development server
- **TanStack Router** - Type-safe routing with file-based routing
- **TanStack Query** - Server state management and data fetching
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - High-quality React components built on Radix UI

### Additional Libraries
- **TS-Rest** - Type-safe API contracts
- **Zod** - Schema validation
- **React Konva** - Canvas manipulation for the canvas editor feature
- **MSW** - API mocking for development and testing
- **Vitest** - Unit testing framework
- **Playwright** - End-to-end testing

## Project Structure

### Route Organization
The project uses TanStack Router's file-based routing with layout routes:

- `_public/` - Public routes (no authentication required)
  - `/` - Home page
  - `/about` - About page
  - `/canva` - Canvas editor feature
  - `/preview` - Preview functionality
  - `/pokemon/` - Pokemon API demo with nested routes
- `_authenticated/` - Protected routes (authentication required)
  - `/home` - Authenticated home
  - `/user/` - User management with nested routes
- `_unauthenticated/` - Routes for non-authenticated users
  - `/sign-in` - Sign in page

### Key Directories
- `src/components/` - Reusable React components
  - `base/` - Basic UI components (buttons, inputs, etc.)
  - `ui/` - Shadcn/ui components
  - `custom/` - Custom project-specific components
  - `editor/` - Canvas editor components
  - `pikaso/` - Canvas manipulation library components
- `src/config/` - Configuration files for routing, queries, i18n, themes
- `src/providers/` - React context providers
- `src/services/` - API services, contracts, and data fetching
- `src/hooks/` - Custom React hooks
- `mock/` - MSW mock handlers for API simulation
- `test/` - Unit tests
- `e2e/` - End-to-end tests

## Development Features

### Internationalization (i18n)
- Supports multiple locales: English (default), French, Spanish
- Uses `use-intl` for internationalization
- Lazy-loaded locale messages
- Locale persistence in localStorage

### Theming
- Dark/light theme support
- CSS custom properties for theming
- Theme persistence in localStorage

### Canvas Editor
- Built with React Konva for canvas manipulation
- Features include shape drawing, image handling, text editing
- Layer management and background customization
- Export/import functionality

### API Integration
- Pokemon API integration as a demo
- Type-safe API contracts with TS-Rest
- MSW for API mocking during development
- TanStack Query for data fetching and caching

## Development Commands

```bash
# Development
pnpm dev              # Start development server on port 3000

# Building
pnpm build           # Build for production
pnpm serve           # Preview production build

# Testing
pnpm test:types      # TypeScript type checking
pnpm test:unit       # Run unit tests with Vitest
pnpm test:unit:ui    # Run unit tests with UI
pnpm test:e2e        # Run end-to-end tests with Playwright
pnpm test:e2e:ui     # Run e2e tests with UI

# Code Quality
pnpm prettier        # Check code formatting
pnpm prettier:write  # Format code
```

## Configuration Notes

### Vite Configuration
- React Compiler enabled for React 19
- Auto code splitting for routes
- Proxy setup for `/api` routes to Pokemon API
- Path aliases configured for clean imports

### TypeScript Configuration
- Strict mode enabled
- Path aliases for `@/*`, `@e2e/*`, `@mock/*`, `@test/*`
- Special alias for `pikaso` library

### Package Management
- Uses `pnpm` as the package manager
- Workspaces configured (includes `../pikaso` workspace)

## Key Features in Development

1. **Canvas Editor** - Advanced drawing and editing capabilities
2. **Authentication Flow** - Route-based authentication system
3. **Pokemon API Demo** - Example of API integration with caching
4. **Responsive Design** - Mobile-first approach with Tailwind CSS
5. **Testing Setup** - Comprehensive testing with unit and e2e tests

## Development Guidelines

- Use file-based routing for new pages
- Follow the established folder structure
- Leverage TanStack Query for all server state
- Use Zod schemas for data validation
- Write tests for new features
- Follow TypeScript strict mode practices
- Use Shadcn/ui components when possible
