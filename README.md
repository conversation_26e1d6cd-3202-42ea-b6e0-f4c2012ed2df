> [!IMPORTANT]
> This boilerplate is still a work in progress

# TanStack Router Boilerplate

This example repo setups opinionated defaults to help me start new projects and test new stuff.

## Tech included so far

- [Shadcn UI](https://ui.shadcn.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [TanStack Query](https://tanstack.com/query/latest)
- [TanStack Router](https://tanstack.com/router/latest)
- [TS-Rest](https://ts-rest.com/)
- [Zod](https://zod.dev/)
