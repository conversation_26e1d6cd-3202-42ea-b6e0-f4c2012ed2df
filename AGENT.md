# Agent Configuration

## Commands
- Build: `pnpm build`
- Dev: `pnpm dev`
- Type check: `pnpm test:types`
- Unit tests: `vitest` (single test: `vitest specific-test-file`)
- E2E tests: `pnpm test:e2e`
- Format: `pnpm prettier:write`

## Code Style (Prettier + Biome)
- 4 spaces, single quotes, semicolons, trailing commas
- Double quotes for JSX, single attribute per line
- 120 char line width
- Import organization: external → internal → relative

## TypeScript
- Strict mode enabled
- Path aliases: `@/*` for src, `@test/*` for test, `@mock/*` for mock
- React JSX transform

## Framework Stack
- TanStack Router (file-based routing)
- React 19, Vite, Vitest, Playwright
- Tai<PERSON>wind CSS, Radix UI, shadcn/ui
- Zustand for state, React Query for data

## Patterns
- Routes in `src/routes/` with `createFileRoute()`
- Components use function declarations
- Import TanStack Router from `@tanstack/react-router`
